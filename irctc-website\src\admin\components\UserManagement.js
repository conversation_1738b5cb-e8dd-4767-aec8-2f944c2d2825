import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { adminAPI, handleApiError } from '../../services/api';
import AdminLayout from './AdminLayout';
import '../styles/UserManagement.css';

const UserManagement = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showAddCreditsModal, setShowAddCreditsModal] = useState(false);
  const [creditsToAdd, setCreditsToAdd] = useState('');

  useEffect(() => {
    fetchUsers();
  }, [currentPage, searchTerm]);

  useEffect(() => {
    if (userId) {
      fetchUserDetails(userId);
    }
  }, [userId]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: 10,
        search: searchTerm
      };
      const response = await adminAPI.getUsers(params);
      setUsers(response.data.users || []);
      setTotalPages(response.data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError(handleApiError(error));
      // Fallback to empty array if API fails
      setUsers([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserDetails = async (id) => {
    try {
      const response = await adminAPI.getUser(id);
      setSelectedUser(response.data.user || response.data);
    } catch (error) {
      console.error('Error fetching user details:', error);
      setError(handleApiError(error));
      // Fallback to finding user in current list
      const user = users.find(u => u._id === id);
      setSelectedUser(user);
    }
  };

  const handleAddCredits = async () => {
    if (!selectedUser || !creditsToAdd) return;

    try {
      const response = await adminAPI.addCredits(selectedUser._id, parseInt(creditsToAdd));
      const updatedUser = response.data.user || response.data;

      setSelectedUser(updatedUser);
      setUsers(users.map(u => u._id === selectedUser._id ? updatedUser : u));
      setShowAddCreditsModal(false);
      setCreditsToAdd('');
    } catch (error) {
      console.error('Error adding credits:', error);
      setError(handleApiError(error));
    }
  };

  const handleStatusChange = async (user, newStatus) => {
    try {
      const response = await adminAPI.updateUserStatus(user._id, newStatus);
      const updatedUser = response.data.user || response.data;
      setUsers(users.map(u => u._id === user._id ? updatedUser : u));
      if (selectedUser && selectedUser._id === user._id) {
        setSelectedUser(updatedUser);
      }
    } catch (error) {
      console.error('Error updating user status:', error);
      setError(handleApiError(error));
    }
  };

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <AdminLayout>
        <div className="admin-loading">
          <div className="loading-spinner"></div>
          <p>Loading users...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="user-management">
        <div className="user-management-header">
          <h1>User Management</h1>
          <p>Manage user accounts and credits</p>
        </div>

        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        <div className="user-management-content">
          {/* Users List */}
          <div className="users-section">
            <div className="users-header">
              <h2>All Users ({filteredUsers.length})</h2>
              <div className="search-box">
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="users-table">
              <table>
                <thead>
                  <tr>
                    <th>User</th>
                    <th>Credits</th>
                    <th>Status</th>
                    <th>Joined</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user._id}>
                      <td>
                        <div className="user-cell">
                          <img 
                            src={user.picture || '/default-avatar.png'} 
                            alt="User" 
                            className="user-avatar-small"
                          />
                          <div>
                            <div className="user-name">{user.name}</div>
                            <div className="user-email">{user.email}</div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span className="credits-badge">
                          {user.credits}
                        </span>
                      </td>
                      <td>
                        <select
                          value={user.status}
                          onChange={(e) => handleStatusChange(user, e.target.value)}
                          className={`status-select ${user.status}`}
                        >
                          <option value="active">Active</option>
                          <option value="inactive">Inactive</option>
                          <option value="suspended">Suspended</option>
                        </select>
                      </td>
                      <td>{new Date(user.createdAt).toLocaleDateString()}</td>
                      <td>
                        <button
                          className="view-btn"
                          onClick={() => {
                            setSelectedUser(user);
                            navigate(`/admin/users/${user._id}`);
                          }}
                        >
                          View Details
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* User Details */}
          {selectedUser && (
            <div className="user-details-section">
              <div className="user-details-header">
                <h2>User Details</h2>
                <button
                  className="close-details-btn"
                  onClick={() => {
                    setSelectedUser(null);
                    navigate('/admin/users');
                  }}
                >
                  ×
                </button>
              </div>

              <div className="user-details-card">
                <div className="user-profile">
                  <img 
                    src={selectedUser.picture || '/default-avatar.png'} 
                    alt="User" 
                    className="user-avatar-large"
                  />
                  <div className="user-info">
                    <h3>{selectedUser.name}</h3>
                    <p>{selectedUser.email}</p>
                    <span className={`status-badge ${selectedUser.status}`}>
                      {selectedUser.status}
                    </span>
                  </div>
                </div>

                <div className="user-stats">
                  <div className="stat-item">
                    <label>Available Credits:</label>
                    <span className="credits-count">{selectedUser.credits}</span>
                  </div>
                  <div className="stat-item">
                    <label>Member Since:</label>
                    <span>{new Date(selectedUser.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>

                <div className="user-actions">
                  <button
                    className="add-credits-btn"
                    onClick={() => setShowAddCreditsModal(true)}
                  >
                    Add Credits
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Add Credits Modal */}
        {showAddCreditsModal && (
          <div className="modal-overlay">
            <div className="modal">
              <div className="modal-header">
                <h3>Add Credits</h3>
                <button
                  className="modal-close"
                  onClick={() => setShowAddCreditsModal(false)}
                >
                  ×
                </button>
              </div>
              <div className="modal-body">
                <p>Add credits to {selectedUser?.name}'s account</p>
                <input
                  type="number"
                  placeholder="Number of credits"
                  value={creditsToAdd}
                  onChange={(e) => setCreditsToAdd(e.target.value)}
                  min="1"
                />
              </div>
              <div className="modal-footer">
                <button
                  className="cancel-btn"
                  onClick={() => setShowAddCreditsModal(false)}
                >
                  Cancel
                </button>
                <button
                  className="confirm-btn"
                  onClick={handleAddCredits}
                  disabled={!creditsToAdd}
                >
                  Add Credits
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default UserManagement;
