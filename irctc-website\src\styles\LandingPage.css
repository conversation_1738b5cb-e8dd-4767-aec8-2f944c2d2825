.landing-page {
  min-height: calc(100vh - 140px); /* Adjust for header/footer */
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
}

.landing-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.landing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo h1 {
  color: white;
  margin: 0;
  font-size: 28px;
  font-weight: 700;
}

.logo p {
  color: rgba(255, 255, 255, 0.8);
  margin: 4px 0 0 0;
  font-size: 14px;
}

.nav-links {
  display: flex;
  gap: 16px;
}

.admin-link {
  color: white;
  text-decoration: none;
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.admin-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Hero Section */
.hero-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  padding: 80px 0;
}

.hero-content h2 {
  color: white;
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 20px 0;
  line-height: 1.2;
}

.hero-content p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 18px;
  margin: 0 0 40px 0;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 300px;
}

.download-btn {
  width:333px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16px 32px;
  background: #28a745;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 18px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  margin-bottom: 8px;
}

.download-btn:hover {
  background: #218838;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
  color: white;
  text-decoration: none;
}

.download-btn.primary {
  background:linear-gradient(135deg, #93aad9 0%, #1a2f5f 100%)
  
}

.cta-subtitle {
  margin-top: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  text-align: center;
  line-height: 1.5;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 300px;
}

.google-btn,
.facebook-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  justify-content: center;
}

.google-btn {
  background-color: white;
  color: #374151;
}

.google-btn:hover:not(:disabled) {
  background-color: #f9fafb;
  transform: translateY(-1px);
}

.facebook-btn {
  background-color: #1877f2;
  color: white;
}

.facebook-btn:hover:not(:disabled) {
  background-color: #166fe5;
  transform: translateY(-1px);
}

.google-btn:disabled,
.facebook-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.google-btn img,
.facebook-btn img {
  width: 20px;
  height: 20px;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-image img {
  max-width: 100%;
  height: auto;
}

/* Features Section */
.features-section {
  background: white;
  padding: 80px 0;
  text-align: center;
}

.features-section h3 {
  color: #1f2937;
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 60px 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.feature-card h4 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.feature-card p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
  line-height: 1.6;
}

/* Pricing Section */
.pricing-section {
  background: #f8fafc;
  padding: 80px 0;
  text-align: center;
}

.pricing-section h3 {
  color: #1f2937;
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 60px 0;
}

.pricing-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  max-width: 900px;
  margin: 0 auto;
}

.pricing-card {
  background: white;
  border-radius: 16px;
  padding: 40px 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid transparent;
  position: relative;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.pricing-card.popular {
  border-color: #3b82f6;
  transform: scale(1.05);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #3b82f6;
  color: white;
  padding: 6px 20px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.pricing-card h4 {
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.price {
  color: #3b82f6;
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.credits {
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
}

.pricing-card p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 30px 0;
}

.price-btn {
  width: 100%;
  padding: 14px 24px;
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(33, 61, 119, 0.3);
}

.price-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 61, 119, 0.4);
}

.price-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* Footer */
.landing-footer {
  background: #1f2937;
  color: white;
  padding: 40px 0;
  text-align: center;
}

.landing-footer p {
  margin: 0 0 20px 0;
  color: #d1d5db;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.footer-links a {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: white;
}

/* Error Message */
.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin: 0 0 20px 0;
  border: 1px solid #fecaca;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing-container {
    padding: 0 16px;
  }

  .hero-section {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
    padding: 60px 0;
  }

  .hero-content h2 {
    font-size: 36px;
  }

  .features-section,
  .pricing-section {
    padding: 60px 0;
  }

  .features-section h3,
  .pricing-section h3 {
    font-size: 28px;
  }

  .pricing-card.popular {
    transform: none;
  }

  .footer-links {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .landing-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .hero-content h2 {
    font-size: 28px;
  }

  .cta-buttons,
  .auth-buttons {
    max-width: 100%;
  }

  .pricing-cards {
    grid-template-columns: 1fr;
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

/* Additional styles for new sections */
.pricing-subtitle {
  text-align: center;
  color: #6b7280;
  font-size: 18px;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.price-per-credit {
  color: #10b981;
  font-size: 14px;
  font-weight: 500;
  margin: 8px 0;
}

/* How it Works Section */
.how-it-works-section {
  padding: 80px 0;
  background: white;
}

.how-it-works-section h3 {
  text-align: center;
  color: #1f2937;
  font-size: 36px;
  margin-bottom: 60px;
  font-weight: 700;
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
}

.step {
  text-align: center;
  padding: 30px 20px;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.step:hover {
  transform: translateY(-5px);
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  margin: 0 auto 20px auto;
}

.step h4 {
  color: #1f2937;
  font-size: 20px;
  margin-bottom: 12px;
  font-weight: 600;
}

.step p {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.5;
}

/* Free Trial Section */
.free-trial-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  margin: 40px 0;
}

.trial-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: center;
}

.trial-content h3 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.trial-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.trial-features {
  margin-bottom: 30px;
}

.trial-feature {
  font-size: 1.1rem;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.trial-btn {
  background: white;
  color: #667eea;
  padding: 15px 30px;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.trial-btn:hover {
  transform: translateY(-2px);
}

.credit-badge {
  background: rgba(255, 255, 255, 0.2);
  border: 3px solid white;
  border-radius: 50%;
  width: 150px;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.credit-number {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
}

.credit-text {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

@media (max-width: 768px) {
  .trial-container {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

