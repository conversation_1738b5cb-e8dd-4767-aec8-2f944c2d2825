let currentPage = 1;
const limit = 10;
let token = localStorage.getItem('adminToken');
let currentFilter = '';

// Check authentication
if (!token) {
    window.location.href = '/login';
}

// Add filter dropdown to HTML
function addFilterDropdown() {
    const filterContainer = document.createElement('div');
    filterContainer.className = 'mb-4';
    filterContainer.innerHTML = `
        <select id="status-filter" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            <option value="">All Users</option>
            <option value="active">Active</option>
            <option value="suspended">Suspended</option>
            <option value="deleted">Deleted</option>
        </select>
    `;
    document.querySelector('.bg-white.shadow.rounded-lg').insertBefore(
        filterContainer,
        document.querySelector('.overflow-x-auto')
    );

    // Add event listener
    document.getElementById('status-filter').addEventListener('change', (e) => {
        currentFilter = e.target.value;
        fetchUsers(1);
    });
}

async function fetchUsers(page = 1) {
    try {
        // Show loading state
        const tbody = document.getElementById('users-table-body');
        tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4">Loading...</td></tr>';

        // Clear any existing error messages
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Check if token exists
        if (!token) {
            throw new Error('No authentication token found. Please log in again.');
        }

        console.log('Fetching users with params:', { page, limit, currentFilter }); // Debug log
        console.log('Using token:', token ? token.substring(0, 10) + '...' : 'No token'); // Debug log (truncated for security)

        const filterParam = currentFilter ? `&status=${currentFilter}` : '';
        const response = await fetch(`/api/admin/users?page=${page}&limit=${limit}${filterParam}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('Response status:', response.status); // Debug log

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Server response:', errorData); // Debug log
            throw new Error(errorData.message || `Failed to fetch users: ${response.status}`);
        }

        const data = await response.json();
        console.log('Received data:', data); // Debug log

        if (!data.users || !Array.isArray(data.users)) {
            throw new Error('Invalid data format received from server');
        }

        tbody.innerHTML = '';

        if (data.users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4">No users found</td></tr>';
            return;
        }

        console.log('Processing users:', data.users.length);
        data.users.forEach((user, index) => {
            try {
                console.log(`Processing user ${index + 1}:`, user.email);
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <img class="h-10 w-10 rounded-full" src="${user.picture || '/img/default-avatar.svg'}" alt="" onerror="this.src='/img/default-avatar.png'">
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${user.name || 'N/A'}</div>
                                <div class="text-sm text-gray-500">Joined: ${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${user.email || 'N/A'}</div>
                        <div class="text-sm text-gray-500">Google: ${user.googleId || 'Not Connected'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <select class="status-select rounded-md border-gray-300 shadow-sm" data-user-id="${user._id}">
                            <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
                            <option value="suspended" ${user.status === 'suspended' ? 'selected' : ''}>Suspended</option>
                            <option value="deleted" ${user.status === 'deleted' ? 'selected' : ''}>Deleted</option>
                        </select>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-500">API Key: ${user.apiKey || 'None'}</div>
                        <div class="text-sm text-gray-500">Last Login: ${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-center">
                            <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                ${user.credits || 0}
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                        <a href="/user-details?id=${user._id}" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            View Details
                        </a>
                    </td>
                `;
                tbody.appendChild(tr);
                console.log(`User ${user.email} row appended to table`);
            } catch (error) {
                console.error(`Error processing user ${index + 1}:`, error);
            }
        });
        console.log('All users processed and added to table');

        // Update pagination
        currentPage = data.currentPage;
        document.getElementById('page-start').textContent = ((currentPage - 1) * limit) + 1;
        document.getElementById('page-end').textContent = Math.min(currentPage * limit, data.totalUsers);
        document.getElementById('total-items').textContent = data.totalUsers;

        // Update buttons
        document.getElementById('prev-page').disabled = currentPage === 1;
        document.getElementById('next-page').disabled = currentPage === data.totalPages;

    } catch (error) {
        console.error('Error fetching users:', error);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative error-message';
        errorDiv.innerHTML = `
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline"> ${error.message}</span>
        `;
        document.querySelector('.overflow-x-auto').prepend(errorDiv);

        tbody.innerHTML = '';

        // If unauthorized, redirect to login
        if (error.message.includes('Authentication') || error.message.includes('401') || error.message.includes('403')) {
            console.log('Authentication error detected, redirecting to login...');
            localStorage.removeItem('adminToken');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000); // Delay to show the error message
        }
    }
}

async function fetchStats() {
    try {
        console.log('Fetching stats...'); // Debug log
        console.log('Using token:', token ? token.substring(0, 10) + '...' : 'No token'); // Debug log (truncated for security)

        // Check if token exists
        if (!token) {
            throw new Error('No authentication token found. Please log in again.');
        }

        const response = await fetch('/api/admin/stats', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('Response status:', response.status); // Debug log
        const data = await response.json();
        console.log('Stats response:', data); // Debug log

        if (!response.ok) {
            throw new Error(data.message || `Failed to fetch stats: ${response.status}`);
        }

        // Update stats in the UI
        document.getElementById('total-users').textContent = data.totalUsers;
        document.getElementById('active-users').textContent = data.activeUsers;
        document.getElementById('new-users').textContent = data.lastWeekUsers;
    } catch (error) {
        console.error('Stats error:', error);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative error-message';
        errorDiv.innerHTML = `
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline"> ${error.message}</span>
        `;

        // Remove any existing error messages before adding new one
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        document.querySelector('.max-w-7xl').prepend(errorDiv);

        // Set stats to 'Error' instead of 0
        document.getElementById('total-users').textContent = 'Error';
        document.getElementById('active-users').textContent = 'Error';
        document.getElementById('new-users').textContent = 'Error';

        // If unauthorized, redirect to login
        if (error.message.includes('Authentication') || error.message.includes('401') || error.message.includes('403')) {
            console.log('Authentication error detected, redirecting to login...');
            localStorage.removeItem('adminToken');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000); // Delay to show the error message
        }
    }
}

// Add token refresh check
function checkToken() {
    token = localStorage.getItem('adminToken');
    if (!token) {
        window.location.href = '/login';
        return false;
    }
    return true;
}

// Initialize filter dropdown
document.addEventListener('DOMContentLoaded', () => {
    if (checkToken()) {
        fetchStats();
        addFilterDropdown();
        fetchUsers(1);
    }
});

// Event Listeners
document.getElementById('prev-page').addEventListener('click', () => {
    if (currentPage > 1) {
        fetchUsers(currentPage - 1);
    }
});

document.getElementById('next-page').addEventListener('click', () => {
    fetchUsers(currentPage + 1);
});

// Status change handler
document.getElementById('users-table-body').addEventListener('change', async (e) => {
    if (e.target.classList.contains('status-select')) {
        const userId = e.target.dataset.userId;
        const newStatus = e.target.value;

        try {
            const response = await fetch(`/api/admin/users/${userId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ status: newStatus })
            });

            if (!response.ok) {
                throw new Error('Failed to update status');
            }

            // Refresh the current page
            fetchUsers(currentPage);
        } catch (error) {
            console.error('Error updating user status:', error);
            alert('Failed to update user status');
        }
    }
});

document.getElementById('logout-btn').addEventListener('click', () => {
    localStorage.removeItem('adminToken');
    window.location.href = '/login';
});

// Refresh data every 5 minutes
setInterval(() => {
    fetchUsers(currentPage);
}, 300000);

// Add periodic refresh for stats
setInterval(fetchStats, 300000); // Refresh every 5 minutes

// Function to view user details
async function viewUserDetails(userId) {
    try {
        if (!userId) {
            throw new Error('User ID is required');
        }

        // Redirect to user details page
        window.location.href = `/user-details?id=${userId}`;
    } catch (error) {
        console.error('Error viewing user details:', error);
        alert(`Error: ${error.message}`);
    }
}
