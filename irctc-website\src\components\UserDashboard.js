import React, { useState, useEffect } from 'react';
import { ticketsAPI, getCurrentUser, handleApiError } from '../services/api';
import PaymentModal from './PaymentModal';
import Layout from './Layout';
import '../styles/UserDashboard.css';

const UserDashboard = () => {
  const [user] = useState(getCurrentUser());
  const [credits, setAvailableTickets] = useState(0);
  const [bookedTickets, setBookedTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [purchasing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [paymentModal, setPaymentModal] = useState({ isOpen: false, package: null });

  // Credit packages
  const creditPackages = [
    { id: 1, name: 'Starter Pack', credits: 5, price: 99 },
    { id: 2, name: 'Value Pack', credits: 20, price: 299, popular: true },
    { id: 3, name: 'Pro Pack', credits: 40, price: 499 }
  ];

  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);

      // Fetch available tickets count
      const ticketsResponse = await ticketsAPI.getCount();
      setAvailableTickets(ticketsResponse.data.count);

      // Fetch booked tickets
      const bookedResponse = await ticketsAPI.getBooked();
      setBookedTickets(bookedResponse.data.tickets);

    } catch (error) {
      console.error('Error fetching user data:', error);
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  };

  const handlePurchaseCredits = (packageData) => {
    setPaymentModal({ isOpen: true, package: packageData });
  };

  const handlePaymentSuccess = (credits) => {
    setAvailableTickets(prev => prev + credits);
    setSuccess(`Successfully purchased ${credits} credits!`);
    setTimeout(() => setSuccess(''), 3000);
    setPaymentModal({ isOpen: false, package: null });
  };

  const handlePaymentClose = () => {
    setPaymentModal({ isOpen: false, package: null });
  };



  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading your dashboard...</p>
      </div>
    );
  }

  return (
    <Layout>
      <div className="user-dashboard">

      <div className="dashboard-content">
        {/* Alerts */}
        {error && <div className="alert alert-error">{error}</div>}
        {success && <div className="alert alert-success">{success}</div>}

        {/* Credits Section */}
        <section className="credits-section">
          <div className="credits-card">
            <h3>Available Credits</h3>
            <div className="credits-count">{credits}</div>
            <p>Use these credits to book Tatkal tickets automatically</p>
          </div>

          <div className="purchase-section">
            <h4>Buy More Credits</h4>
            <div className="purchase-options">
              {creditPackages.map((pkg) => (
                <div key={pkg.id} className={`purchase-card ${pkg.popular ? 'popular' : ''}`}>
                  {pkg.popular && <div className="popular-badge">Best Value</div>}
                  <h5>{pkg.credits} Credits</h5>
                  <div className="price">₹{pkg.price}</div>
                  <div className="price-per-credit">₹{(pkg.price / pkg.credits).toFixed(2)} per credit</div>
                  <button
                    className="purchase-btn"
                    onClick={() => handlePurchaseCredits(pkg)}
                    disabled={purchasing}
                  >
                    Buy Now
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Extension Download */}
        <section className="extension-section">
          <div className="extension-card">
            <h3>📥 Download Chrome Extension</h3>
            <p>Install our Chrome extension to start booking Tatkal tickets automatically</p>
            <a
              href="https://chrome.google.com/webstore/detail/irctc-tatkal-extension/YOUR_EXTENSION_ID"
              target="_blank"
              rel="noopener noreferrer"
              className="download-extension-btn"
            >
              Download from Chrome Web Store
            </a>
          </div>
        </section>

        {/* Extension Guide */}
        <section className="guide-section">
          <h3>How to Use the Extension</h3>
          <div className="guide-steps">
            <div className="step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Install Extension</h4>
                <p>Download and install our Chrome extension from the Chrome Web Store</p>
              </div>
            </div>
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Login to Extension</h4>
                <p>Use your API key to login to the extension: <code>{user?.apiKey}</code></p>
              </div>
            </div>
            <div className="step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Book Tickets</h4>
                <p>Fill the booking form in the extension and let it handle the rest</p>
              </div>
            </div>
          </div>
        </section>

        {/* Recent Bookings */}
        <section className="bookings-section">
          <h3>Recent Bookings</h3>
          {bookedTickets.length > 0 ? (
            <div className="bookings-list">
              {bookedTickets.slice(0, 5).map((ticket) => (
                <div key={ticket._id} className="booking-card">
                  <div className="booking-info">
                    <h4>{ticket.fromStation} → {ticket.toStation}</h4>
                    <p>Journey Date: {formatDate(ticket.journeyDate)}</p>
                    <p>Class: {ticket.class}</p>
                    {ticket.pnr && <p>PNR: {ticket.pnr}</p>}
                  </div>
                  <div className="booking-status">
                    <span className={`status ${ticket.status || 'confirmed'}`}>
                      {ticket.status || 'Confirmed'}
                    </span>
                    <p>Booked: {formatDate(ticket.bookingDate)}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-bookings">
              <p>No bookings yet. Start using the extension to see your bookings here.</p>
            </div>
          )}
        </section>

        {/* API Key Section */}
        <section className="api-section">
          <h3>Extension API Key</h3>
          <div className="api-card">
            <p>Use this API key to authenticate with the Chrome extension:</p>
            <div className="api-key">
              <code>{user?.apiKey}</code>
              <button
                className="copy-btn"
                onClick={() => navigator.clipboard.writeText(user?.apiKey)}
              >
                Copy
              </button>
            </div>
            <p className="api-note">
              Keep this key secure and don't share it with anyone.
            </p>
          </div>
        </section>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={paymentModal.isOpen}
        onClose={handlePaymentClose}
        creditPackage={paymentModal.package}
        onSuccess={handlePaymentSuccess}
      />
      </div>
    </Layout>
  );
};

export default UserDashboard;
