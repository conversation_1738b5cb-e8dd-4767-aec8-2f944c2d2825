.user-management {
  max-width: 1400px;
  margin: 0 auto;
}

.user-management-header {
  margin-bottom: 32px;
}

.user-management-header h1 {
  color: #1f2937;
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.user-management-header p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.user-management-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 32px;
}

/* Users Section */
.users-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.users-header h2 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.search-box input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  width: 250px;
  transition: all 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #213d77;
  box-shadow: 0 0 0 3px rgba(33, 61, 119, 0.1);
}

/* Users Table */
.users-table {
  overflow-x: auto;
}

.users-table table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  text-align: left;
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.users-table th {
  background: #f9fafb;
  color: #374151;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.users-table td {
  color: #6b7280;
  font-size: 14px;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  color: #1f2937;
  font-weight: 500;
  font-size: 14px;
}

.user-email {
  color: #6b7280;
  font-size: 12px;
}

.credits-badge {
  background: #f0f4ff;
  color: #213d77;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  cursor: pointer;
}

.status-select.active {
  color: #065f46;
  background: #d1fae5;
  border-color: #a7f3d0;
}

.status-select.inactive {
  color: #92400e;
  background: #fef3c7;
  border-color: #fde68a;
}

.status-select.suspended {
  color: #991b1b;
  background: #fee2e2;
  border-color: #fecaca;
}

.view-btn {
  background: #213d77;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn:hover {
  background: #1a2f5f;
}

/* User Details Section */
.user-details-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  height: fit-content;
}

.user-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-details-header h2 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.close-details-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-details-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.user-details-card {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.user-avatar-large {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info h3 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.user-info p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 8px 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.status-badge.active {
  background: #d1fae5;
  color: #065f46;
}

.status-badge.inactive {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.suspended {
  background: #fee2e2;
  color: #991b1b;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item label {
  color: #6b7280;
  font-size: 14px;
}

.credits-count {
  background: #213d77;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
}

.user-actions {
  display: flex;
  gap: 12px;
}

.add-credits-btn {
  background: #213d77;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-credits-btn:hover {
  background: #1a2f5f;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  margin: 20px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.modal-body p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 16px 0;
}

.modal-body input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box;
}

.modal-body input:focus {
  outline: none;
  border-color: #213d77;
  box-shadow: 0 0 0 3px rgba(33, 61, 119, 0.1);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  background: #213d77;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn:hover:not(:disabled) {
  background: #1a2f5f;
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .user-management-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .search-box input {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .users-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .search-box input {
    width: 100%;
  }
  
  .user-profile {
    flex-direction: column;
    text-align: center;
  }
  
  .user-actions {
    justify-content: center;
  }
}
