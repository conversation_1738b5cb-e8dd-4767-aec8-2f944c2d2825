{"name": "irctc-auto-booking-extension", "version": "1.0.0", "description": "Chrome extension for automated IRCTC ticket booking", "scripts": {"build:dev": "node build-config.js development", "build:prod": "node build-config.js production", "build": "npm run build:dev", "package:dev": "npm run build:dev && npm run zip:dev", "package:prod": "npm run build:prod && npm run zip:prod", "zip:dev": "zip -r irctc-extension-dev.zip . -x node_modules/\\* .git/\\* *.zip build-config.js package.json package-lock.json", "zip:prod": "zip -r irctc-extension-prod.zip . -x node_modules/\\* .git/\\* *.zip build-config.js package.json package-lock.json"}, "keywords": ["chrome-extension", "irctc", "automation", "booking"], "author": "Your Name", "license": "MIT", "devDependencies": {}}