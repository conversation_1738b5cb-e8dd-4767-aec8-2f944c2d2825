const nodemailer = require('nodemailer');

// Create transporter (fix: createTransport not createTransporter)
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  },
  debug: true, // Enable debug logs
  logger: true // Enable logger
});

// Test connection
transporter.verify((error, success) => {
  if (error) {
    console.error('❌ Email configuration error:', error);
  } else {
    console.log('✅ Email server is ready to send messages');
  }
});

const sendPurchaseConfirmationEmail = async (userDetails, purchaseData) => {
  const mailOptions = {
    from: `"IRCTC Auto" <${process.env.EMAIL_USER}>`,
    to: userDetails.email,
    subject: 'Payment Successful - Your IRCTC Auto Credits',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>🎉 Payment Successful!</h2>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>Purchase Details</h3>
          <p><strong>Package:</strong> ${purchaseData.packageName}</p>
          <p><strong>Credits Purchased:</strong> ${purchaseData.credits}</p>
          <p><strong>Amount Paid:</strong> ₹${purchaseData.amount}</p>
          <p><strong>Email:</strong> ${userDetails.email}</p>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>✅ Credits Added Successfully</h3>
          <p>Credits purchased: <strong>${purchaseData.credits}</strong></p>
          <p>Your account now has <strong>${purchaseData.totalCredits} total credits</strong> ready to use!</p>
        </div>

        <div style="margin: 20px 0;">
          <h3>Next Steps</h3>
          <ol>
            <li>Download our Chrome extension</li>
            <li>Login with your email: <strong>${userDetails.email}</strong></li>
            <li>Start booking tickets automatically</li>
          </ol>
        </div>

        <p>Need help? Contact <NAME_EMAIL></p>
      </div>
    `
  };

  try {
    console.log('📧 Attempting to send email to:', userDetails.email);
    const result = await transporter.sendMail(mailOptions);
    console.log('✅ Purchase confirmation email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    console.error('❌ Error sending email:', error.message);
    console.error('Full error:', error);
    throw error;
  }
};

module.exports = {
  sendPurchaseConfirmationEmail
};







