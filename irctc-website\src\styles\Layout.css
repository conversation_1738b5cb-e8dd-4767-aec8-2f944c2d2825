/* Layout Styles */
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.layout-header {
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  padding: 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 70px;
}

.logo a {
  text-decoration: none;
  color: inherit;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: white;
}

.logo p {
  margin: 2px 0 0 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* Navigation */
.main-nav {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 24px;
}

.nav-link {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.admin-link {
  color: white;
  text-decoration: none;
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.admin-link:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* User Menu */
.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
}

.user-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.logout-btn-small {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn-small:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Main Content */
.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Footer */
.layout-footer {
  background: #2d3748;
  color: white;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px 20px 24px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3 {
  color: white;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
}

.footer-section h4 {
  color: white;
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.footer-section p {
  color: #a0aec0;
  margin: 0;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #a0aec0;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid #4a5568;
  padding-top: 20px;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  color: #a0aec0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
    flex-direction: column;
    gap: 16px;
    min-height: auto;
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .logo {
    text-align: center;
  }

  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }

  .user-menu {
    flex-direction: column;
    gap: 8px;
    padding: 12px;
  }

  .user-name {
    font-size: 12px;
  }

  .footer-content {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
  }

  .footer-container {
    padding: 30px 16px 15px 16px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 12px;
  }

  .logo h1 {
    font-size: 20px;
  }

  .logo p {
    font-size: 11px;
  }

  .nav-links {
    gap: 12px;
  }

  .nav-link {
    padding: 6px 12px;
    font-size: 14px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .footer-section {
    text-align: center;
  }
}
