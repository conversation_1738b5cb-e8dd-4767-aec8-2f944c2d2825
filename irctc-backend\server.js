require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
const rateLimit = require('express-rate-limit');
const connectDB = require('./config/db');

const app = express();

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});

// Middleware
app.use(express.json({ limit: '10kb' }));
// Configure CORS based on environment
const allowedOrigins = process.env.NODE_ENV === 'production'
    ? [process.env.FRONTEND_URL_PROD]
    : [process.env.FRONTEND_URL_DEV || 'http://localhost:3001', 'http://localhost:3000'];

app.use(cors({
    origin: [
        'chrome-extension://*', // Allow any chrome extension during development
        process.env.FRONTEND_URL_PROD,
        process.env.FRONTEND_URL_DEV || 'http://localhost:3001',
        'http://localhost:3000'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(limiter);
app.use(express.static('public'));

// Handle preflight requests
app.options('*', cors());

// Add this before your routes
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin);
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', true);
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Root route
app.get('/', (req, res) => {
    res.json({
        status: 'success',
        message: 'IRCTC Backend API',
        endpoints: {
            auth: '/api/auth',
            users: '/api/users',
            admin: '/api/admin',
            health: '/health'
        }
    });
});

// Health check route
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'ok', message: 'Server is running' });
});

// API base route
app.get('/api', (req, res) => {
    res.json({
        status: 'success',
        message: 'IRCTC Backend API',
        version: '1.0.0',
        endpoints: {
            auth: {
                google: 'POST /api/auth/google',
                facebook: 'POST /api/auth/facebook'
            },
            users: {
                credits: 'GET /api/users/credits'
            },
            tickets: {
                count: 'GET /api/tickets/count',
                book: 'POST /api/tickets/book',
                booked: 'GET /api/tickets/booked',
                cancel: 'POST /api/tickets/cancel/:id'
            },
            payment: {
                createOrder: 'POST /api/payment/create-order',
                verify: 'POST /api/payment/verify'
            },
            admin: {
                login: 'POST /api/admin/login',
                users: 'GET /api/admin/users'
            }
        },
        documentation: 'Visit the root path (/) for more information'
    });
});

// Routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const adminRoutes = require('./routes/admin');
const ticketsRoutes = require('./routes/tickets');
const paymentRoutes = require('./routes/payment');

app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/admin', adminRoutes); // This mounts all admin routes under /api/admin
app.use('/api/tickets', ticketsRoutes); // This mounts all tickets routes under /api/tickets
app.use('/api/payment', paymentRoutes); // This mounts all payment routes under /api/payment

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Handle HTML routes
app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

app.get('/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'dashboard.html'));
});

app.get('/user-details', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'user-details.html'));
});

app.get('/test-users', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'test-users.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(err.status || 500).json({
        status: 'error',
        message: err.message || 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
});

// Handle 404 routes
app.use('*', (req, res) => {
    res.status(404).json({
        status: 'error',
        message: 'Route not found'
    });
});

// Connect to MongoDB
connectDB();

const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
    console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
    console.error('UNHANDLED REJECTION! 💥 Shutting down...');
    console.error(err.name, err.message);
    server.close(() => {
        process.exit(1);
    });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('UNCAUGHT EXCEPTION! 💥 Shutting down...');
    console.error(err.name, err.message);
    process.exit(1);
});

console.log('Environment check:', {
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID ? 'set' : 'missing',
    JWT_SECRET: process.env.JWT_SECRET ? 'set' : 'missing'
});
