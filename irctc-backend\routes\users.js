const express = require('express');
const router = express.Router();
const User = require('../models/User');
const auth = require('../middleware/auth');

// Update user preferences
router.patch('/preferences', auth, async (req, res) => {
    try {
        const updates = Object.keys(req.body);
        const allowedUpdates = ['paymentMethods', 'autoFillEnabled'];
        const isValidOperation = updates.every(update => allowedUpdates.includes(update));

        if (!isValidOperation) {
            return res.status(400).json({ error: 'Invalid updates' });
        }

        updates.forEach(update => {
            req.user.preferences[update] = req.body[update];
        });
        await req.user.save();

        res.json(req.user);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});

// Get user credits
router.get('/credits', auth, async (req, res) => {
    try {
        const user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.json({
            success: true,
            credits: user.credits || 0,
            message: 'Credits retrieved successfully'
        });
    } catch (error) {
        console.error('Error fetching user credits:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch credits',
            error: error.message
        });
    }
});

// Regenerate API key
router.post('/api-key/regenerate', auth, async (req, res) => {
    try {
        req.user.apiKey = require('crypto').randomBytes(32).toString('hex');
        await req.user.save();
        res.json({ apiKey: req.user.apiKey });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;