const mongoose = require('mongoose');

const transactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // Allow guest transactions without userId
  },
  orderId: {
    type: String,
    required: true,
    unique: true
  },
  paymentId: {
    type: String,
    default: null
  },
  signature: {
    type: String,
    default: null
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'INR'
  },
  credits: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['created', 'pending', 'completed', 'failed', 'cancelled'],
    default: 'created'
  },
  type: {
    type: String,
    enum: ['credit_purchase', 'guest_credit_purchase', 'refund', 'bonus'],
    default: 'credit_purchase'
  },
  description: {
    type: String,
    default: ''
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  // Guest transaction details (for transactions without userId)
  guestDetails: {
    email: {
      type: String,
      required: false
    },
    name: {
      type: String,
      required: false
    },
    phone: {
      type: String,
      required: false
    }
  }
}, {
  timestamps: true
});

// Indexes for better query performance
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ orderId: 1 });
transactionSchema.index({ paymentId: 1 });
transactionSchema.index({ status: 1 });

// Virtual for formatted amount
transactionSchema.virtual('formattedAmount').get(function() {
  return `₹${this.amount.toFixed(2)}`;
});

// Virtual for transaction reference
transactionSchema.virtual('reference').get(function() {
  return this.paymentId || this.orderId;
});

// Instance method to check if transaction is successful
transactionSchema.methods.isSuccessful = function() {
  return this.status === 'completed';
};

// Instance method to check if transaction is pending
transactionSchema.methods.isPending = function() {
  return ['created', 'pending'].includes(this.status);
};

// Static method to get user's transaction summary
transactionSchema.statics.getUserSummary = async function(userId) {
  const summary = await this.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        totalCredits: { $sum: '$credits' }
      }
    }
  ]);

  const result = {
    total: 0,
    completed: { count: 0, amount: 0, credits: 0 },
    pending: { count: 0, amount: 0, credits: 0 },
    failed: { count: 0, amount: 0, credits: 0 }
  };

  summary.forEach(item => {
    result.total += item.count;
    if (result[item._id]) {
      result[item._id] = {
        count: item.count,
        amount: item.totalAmount,
        credits: item.totalCredits
      };
    }
  });

  return result;
};

// Pre-save middleware to set description if not provided
transactionSchema.pre('save', function(next) {
  if (!this.description && this.type === 'credit_purchase' && this.credits) {
    this.description = `Purchase of ${this.credits} credits`;
  }
  next();
});

module.exports = mongoose.model('Transaction', transactionSchema);
