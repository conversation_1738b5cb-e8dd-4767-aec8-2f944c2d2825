.purchase-page {
  min-height: calc(100vh - 140px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 40px 0;
}

.purchase-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Header */
.purchase-header {
  text-align: center;
  margin-bottom: 40px;
}

.back-btn {
  background: none;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 24px;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.purchase-header h1 {
  color: #1f2937;
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.purchase-header p {
  color: #6b7280;
  font-size: 18px;
  margin: 0;
}

/* Content Layout */
.purchase-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 60px;
}

/* Package Summary Card */
.package-summary-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  height: fit-content;
}

.package-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.package-header h2 {
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.popular-badge {
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.package-details {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item.total {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
  margin-top: 12px;
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
}

.detail-item .label {
  color: #6b7280;
}

.detail-item .value {
  color: #1f2937;
  font-weight: 500;
}

.package-benefits h3 {
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.package-benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.package-benefits li {
  color: #374151;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.5;
}

.package-benefits li:last-child {
  margin-bottom: 0;
}

/* User Form Card */
.user-form-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  height: fit-content;
}

.user-form-card h2 {
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 24px 0;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
}

.error-message p {
  margin: 0;
  color: #dc2626;
  font-size: 14px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #213d77;
  box-shadow: 0 0 0 3px rgba(33, 61, 119, 0.1);
}

.form-group small {
  display: block;
  margin-top: 4px;
  color: #6b7280;
  font-size: 12px;
}

.payment-info {
  margin: 24px 0;
}

.security-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.badge {
  background: #f0f4ff;
  color: #213d77;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #c7d2fe;
}

.pay-button {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(33, 61, 119, 0.3);
  margin-bottom: 16px;
}

.pay-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 61, 119, 0.4);
}

.pay-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.terms-notice {
  text-align: center;
}

.terms-notice p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.terms-notice a {
  color: #213d77;
  text-decoration: none;
}

.terms-notice a:hover {
  text-decoration: underline;
}

/* Trust Section */
.trust-section {
  text-align: center;
  background: white;
  border-radius: 16px;
  padding: 40px 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.trust-section h3 {
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 32px 0;
}

.trust-indicators {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 32px;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 16px;
  text-align: left;
}

.indicator .icon {
  font-size: 32px;
  flex-shrink: 0;
}

.indicator .text strong {
  display: block;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.indicator .text p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .purchase-container {
    padding: 0 16px;
  }
  
  .purchase-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .package-summary-card,
  .user-form-card {
    padding: 24px;
  }
  
  .trust-indicators {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .indicator {
    justify-content: center;
    text-align: center;
  }
  
  .security-badges {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .purchase-page {
    padding: 20px 0;
  }
  
  .purchase-header h1 {
    font-size: 28px;
  }
  
  .purchase-header p {
    font-size: 16px;
  }
  
  .package-summary-card,
  .user-form-card,
  .trust-section {
    padding: 20px;
  }
}
