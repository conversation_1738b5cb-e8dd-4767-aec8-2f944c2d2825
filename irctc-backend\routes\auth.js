const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const fetch = require('node-fetch');
const { OAuth2Client } = require('google-auth-library');
const auth = require('../middleware/auth');

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

// Google OAuth login
router.post('/google', async (req, res) => {
    try {
        const { token, name, email, googleId, picture } = req.body;

        // Debug logs
        console.log('Request body:', {
            tokenLength: token ? token.length : 0,
            name,
            email,
            googleId
        });

        if (!token) {
            return res.status(400).json({ error: 'Token is required' });
        }

        try {
            // Verify the token using tokeninfo endpoint
            const tokenInfoResponse = await fetch(
                `https://oauth2.googleapis.com/tokeninfo?access_token=${token}`
            );

            if (!tokenInfoResponse.ok) {
                throw new Error('Invalid token');
            }

            const tokenInfo = await tokenInfoResponse.json();

            // Find or create user
            let user = await User.findOne({ email: email });

            if (!user) {
                // Create new user
                user = new User({
                    name,
                    email,
                    googleId,
                    picture,
                    apiKey: require('crypto').randomBytes(32).toString('hex'),
                    status: 'active'
                });
                await user.save();
            } else {
                // Update existing user
                user.lastLogin = new Date();
                user.picture = picture;
                user.googleId = googleId;
                await user.save();
            }

            // Generate JWT token
            const authToken = jwt.sign(
                { userId: user._id },
                process.env.JWT_SECRET,
                { expiresIn: '7d' } // Increased to 7 days for better user experience
            );

            res.json({
                token: authToken,
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    picture: user.picture,
                    apiKey: user.apiKey
                }
            });

        } catch (verificationError) {
            console.error('Token verification error:', verificationError);
            return res.status(401).json({
                error: 'Token verification failed',
                details: verificationError.message
            });
        }

    } catch (error) {
        console.error('Google auth error:', error);
        res.status(401).json({
            error: 'Authentication failed',
            details: error.message
        });
    }
});

// Facebook OAuth login
router.post('/facebook', async (req, res) => {
    try {
        const { accessToken, name, email, facebookId, picture } = req.body;

        // Debug logs
        console.log('Facebook auth request:', {
            tokenLength: accessToken ? accessToken.length : 0,
            name,
            email,
            facebookId
        });

        if (!accessToken) {
            return res.status(400).json({ error: 'Access token is required' });
        }

        try {
            // Verify the token with Facebook
            const response = await fetch(`https://graph.facebook.com/me?access_token=${accessToken}&fields=id,name,email`);

            if (!response.ok) {
                throw new Error('Invalid Facebook token');
            }

            const fbData = await response.json();

            if (fbData.id !== facebookId) {
                throw new Error('Facebook ID mismatch');
            }

            // Find or create user
            let user = await User.findOne({ email: email });

            if (!user) {
                // Create new user
                user = new User({
                    name,
                    email,
                    facebookId,
                    picture,
                    apiKey: require('crypto').randomBytes(32).toString('hex'),
                    status: 'active'
                });
                await user.save();
            } else {
                // Update existing user
                user.lastLogin = new Date();
                user.picture = picture;
                user.facebookId = facebookId;
                await user.save();
            }

            // Generate JWT token
            const authToken = jwt.sign(
                { userId: user._id },
                process.env.JWT_SECRET,
                { expiresIn: '7d' } // Increased to 7 days for better user experience
            );

            res.json({
                token: authToken,
                user: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    picture: user.picture,
                    apiKey: user.apiKey
                }
            });

        } catch (verificationError) {
            console.error('Facebook token verification error:', verificationError);
            return res.status(401).json({
                error: 'Token verification failed',
                details: verificationError.message
            });
        }

    } catch (error) {
        console.error('Facebook auth error:', error);
        res.status(401).json({
            error: 'Authentication failed',
            details: error.message
        });
    }
});

// Get current user
router.get('/me', auth, async (req, res) => {
    try {
        const user = await User.findById(req.user._id).select('-password');
        res.json(user);
    } catch (error) {
        res.status(500).json({ error: 'Server error' });
    }
});

module.exports = router;

