require('dotenv').config();
const { sendPurchaseConfirmationEmail } = require('./services/emailService');

// Test email function
async function testEmail() {
  try {
    console.log('Testing email with credentials:');
    console.log('EMAIL_USER:', process.env.EMAIL_USER);
    console.log('EMAIL_PASS:', process.env.EMAIL_PASS ? 'Set' : 'Not set');

    await sendPurchaseConfirmationEmail(
      {
        name: 'Test User',
        email: '<EMAIL>', // Replace with your actual email
        phone: '9999999999'
      },
      {
        packageName: '10 Credits Package',
        credits: 10,
        amount: 100
      }
    );

    console.log('✅ Email test completed');
  } catch (error) {
    console.error('❌ Email test failed:', error);
  }
}

testEmail();
