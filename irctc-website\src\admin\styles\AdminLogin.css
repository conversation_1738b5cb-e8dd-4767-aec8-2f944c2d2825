.admin-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.admin-login-container {
  width: 100%;
  max-width: 400px;
}

.admin-login-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.admin-login-header {
  text-align: center;
  margin-bottom: 32px;
}

.admin-login-header h1 {
  color: #1f2937;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.admin-login-header p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.admin-login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
}

.form-group input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus {
  outline: none;
  border-color: #213d77;
  box-shadow: 0 0 0 3px rgba(33, 61, 119, 0.1);
}

.form-group input:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

.error-message p {
  margin: 0;
  color: #dc2626;
  font-size: 14px;
}

.admin-login-btn {
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 14px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(33, 61, 119, 0.3);
}

.admin-login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 61, 119, 0.4);
}

.admin-login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.admin-login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.back-link {
  color: #213d77;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.back-link:hover {
  color: #1a2f5f;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 480px) {
  .admin-login {
    padding: 16px;
  }
  
  .admin-login-card {
    padding: 32px 24px;
  }
  
  .admin-login-header h1 {
    font-size: 24px;
  }
}
