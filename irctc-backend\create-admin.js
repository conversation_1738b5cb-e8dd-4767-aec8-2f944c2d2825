require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Admin = require('./models/Admin');
const connectDB = require('./config/db');

// Connect to MongoDB
connectDB();

const createAdmin = async () => {
    try {
        // Check if admin already exists
        const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });
        
        if (existingAdmin) {
            console.log('Admin already exists');
            return;
        }
        
        // Create a new admin
        const admin = new Admin({
            name: 'Admin User',
            email: '<EMAIL>',
            password: 'admin123' // This will be hashed by the pre-save hook
        });
        
        await admin.save();
        console.log('Admin created successfully');
        
    } catch (error) {
        console.error('Error creating admin:', error);
    } finally {
        // Close the connection
        mongoose.connection.close();
        console.log('Database connection closed');
    }
};

createAdmin();
