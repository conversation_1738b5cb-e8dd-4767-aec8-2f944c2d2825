import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { getCurrentUser, logout, isAuthenticated } from '../services/api';
import '../styles/Layout.css';

const Layout = ({ children, showHeader = true, showFooter = true, showFreeTrial = false }) => {
  const location = useLocation();
  const user = getCurrentUser();
  const isLoggedIn = isAuthenticated();

  const handleLogout = () => {
    logout();
  };

  const isActivePage = (path) => {
    return location.pathname === path;
  };

  return (
    <div className="layout">
      {showHeader && (
        <header className="layout-header">
          <div className="header-container">
            <div className="logo">
              <Link to="/">
                <h1>IRCTC Auto</h1>
                <p>Booking made simple</p>
              </Link>
            </div>
            
            <nav className="main-nav">
              <div className="nav-links">
                <Link 
                  to="/" 
                  className={`nav-link ${isActivePage('/') ? 'active' : ''}`}
                >
                  Home
                </Link>
                <Link 
                  to="/support" 
                  className={`nav-link ${isActivePage('/support') ? 'active' : ''}`}
                >
                  Support
                </Link>
                {isLoggedIn && (
                  <>
                    <Link
                      to="/dashboard"
                      className={`nav-link ${isActivePage('/dashboard') ? 'active' : ''}`}
                    >
                      Dashboard
                    </Link>
                    <div className="user-menu">
                      <img
                        src={user?.picture || '/default-avatar.png'}
                        alt="Profile"
                        className="user-avatar-small"
                      />
                      <span className="user-name">{user?.name}</span>
                      <button className="logout-btn-small" onClick={handleLogout}>
                        Logout
                      </button>
                    </div>
                  </>
                )}
              </div>
            </nav>
          </div>
        </header>
      )}

      <main className="layout-main">
        {children}
      </main>

      {showFooter && (
        <footer className="layout-footer">
          <div className="footer-container">
            <div className="footer-content">
              <div className="footer-section">
                <h3>IRCTC Auto</h3>
                <p>Your smart booking companion for IRCTC.</p>
              </div>
              
              <div className="footer-section">
                <h4>Quick Links</h4>
                <ul>
                  <li><Link to="/">Home</Link></li>
                  <li><Link to="/support">Support</Link></li>
                  <li><Link to="/setup">Setup Guide</Link></li>
                </ul>
              </div>
              
              <div className="footer-section">
                <h4>Legal</h4>
                <ul>
                  <li><Link to="/privacy">Privacy</Link></li>
                  <li><Link to="/terms">Terms</Link></li>
                  <li><Link to="/refund">Refund</Link></li>
                </ul>
              </div>
              
              <div className="footer-section">
                <h4>Contact</h4>
                <ul>
                  <li>Email: <EMAIL></li>
                </ul>
              </div>
            </div>
            
            <div className="footer-bottom">
              <p>&copy; 2025 IRCTC Auto. All rights reserved.</p>
            </div>
          </div>
        </footer>
      )}
    </div>
  );
};

export default Layout;

