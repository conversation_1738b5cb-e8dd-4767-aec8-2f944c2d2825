/* Static Pages Styles */
.static-page {
  min-height: calc(100vh - 140px); /* Adjust for header/footer */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px 0;
}

.static-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 24px;
}  

/* Header */
.static-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  text-align: center;
}

.back-link {
  display: inline-block;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 20px;
  padding: 8px 16px;
  border: 1px solid #667eea;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.back-link:hover {
  background: #667eea;
  color: white;
}

.static-header h1 {
  color: #2d3748;
  margin: 0 0 10px 0;
  font-size: 36px;
  font-weight: 700;
}

.last-updated, .subtitle {
  color: #718096;
  margin: 0;
  font-size: 16px;
}

/* Content */
.static-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  line-height: 1.6;
}

.static-content section {
  margin-bottom: 40px;
}

.static-content section:last-child {
  margin-bottom: 0;
}

.static-content h2 {
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.static-content h3 {
  color: #4a5568;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
}

.static-content p {
  color: #4a5568;
  margin-bottom: 16px;
}

.static-content ul {
  margin-bottom: 16px;
  padding-left: 20px;
}

.static-content li {
  color: #4a5568;
  margin-bottom: 8px;
}

.static-content strong {
  color: #2d3748;
  font-weight: 600;
}

.contact-info {
  background: #f7fafc;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.contact-info p {
  margin-bottom: 8px;
}

.contact-info a {
  color: #667eea;
  text-decoration: none;
}

.contact-info a:hover {
  text-decoration: underline;
}

/* Support Page Specific Styles */
.support-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #718096;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn.active {
  background: #667eea;
  color: white;
}

.tab-btn:hover:not(.active) {
  background: #f7fafc;
  color: #4a5568;
}

/* FAQ Section */
.faq-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.faq-item {
  background: #f7fafc;
  padding: 24px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.faq-item h3 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 18px;
}

.faq-item p {
  margin-bottom: 0;
  color: #4a5568;
}

/* Contact Section */
.contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.contact-method {
  background: #f7fafc;
  padding: 24px;
  border-radius: 8px;
  text-align: center;
}

.contact-method h3 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 18px;
}

.contact-method p {
  color: #718096;
  margin-bottom: 8px;
}

.contact-method a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.contact-method a:hover {
  text-decoration: underline;
}

/* Contact Form */
.contact-form-container {
  background: #f7fafc;
  padding: 30px;
  border-radius: 8px;
}

.contact-form-container h3 {
  margin-bottom: 24px;
  color: #2d3748;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #4a5568;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.submit-btn {
  background: #667eea;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.submit-btn:hover {
  background: #5a67d8;
}

.form-success {
  text-align: center;
  padding: 40px;
}

.form-success h4 {
  color: #38a169;
  margin-bottom: 12px;
  font-size: 20px;
}

.form-success p {
  color: #4a5568;
}

/* Guides Section */
.guide-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.guide-card {
  background: #f7fafc;
  padding: 24px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.guide-card h3 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 18px;
}

.guide-card p {
  color: #718096;
  margin-bottom: 16px;
}

.guide-card ul {
  margin-bottom: 0;
}

.guide-card li {
  color: #4a5568;
  margin-bottom: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .static-container {
    padding: 0 16px;
  }

  .static-header {
    padding: 20px;
  }

  .static-header h1 {
    font-size: 28px;
  }

  .static-content {
    padding: 24px;
  }

  .support-tabs {
    flex-direction: column;
  }

  .tab-btn {
    margin-bottom: 4px;
  }

  .contact-methods {
    grid-template-columns: 1fr;
  }

  .contact-form-container {
    padding: 20px;
  }

  .guide-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .static-header h1 {
    font-size: 24px;
  }

  .static-content {
    padding: 16px;
  }

  .static-content h2 {
    font-size: 20px;
  }
}

.process-steps {
  display: grid;
  gap: 1.5rem;
  margin: 2rem 0;
}

.step {
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #f9f9f9;
}

.step h3 {
  color: #213d77;
  margin-bottom: 0.5rem;
}

.timeline {
  display: grid;
  gap: 1rem;
  margin: 1.5rem 0;
}

.timeline-item {
  padding: 1rem;
  background: #f0f4f8;
  border-left: 4px solid #213d77;
  border-radius: 4px;
}

.contact-info {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.contact-info p {
  margin: 0.5rem 0;
}

.cta-section {
  text-align: center;
  padding: 2rem;
  background: #f0f4f8;
  border-radius: 8px;
  margin-top: 2rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #213d77;
  color: white;
}

.btn-secondary {
  background: transparent;
  color: #213d77;
  border: 2px solid #213d77;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Setup Guide Styles */
.setup-guide {
  max-width: 800px;
  margin: 0 auto;
}

.progress-bar {
  display: flex;
  justify-content: space-between;
  margin: 2rem 0;
  position: relative;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 2;
  background: white;
  padding: 0 10px;
}

.progress-step.active .step-number {
  background: #213d77;
  color: white;
}

.progress-step.current .step-number {
  background: #667eea;
  color: white;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e0e0e0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.step-title {
  font-size: 12px;
  text-align: center;
  color: #666;
  max-width: 100px;
}

.step-container {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin: 2rem 0;
}

.instruction-block {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.download-section {
  text-align: center;
  margin: 1.5rem 0;
}

.download-btn {
  display: inline-block;
  background: #213d77;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.download-btn:hover {
  background: #1a2f5f;
  transform: translateY(-2px);
}

.credit-packages {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.package {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
  text-align: center;
  position: relative;
}

.package.popular {
  border-color: #213d77;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #213d77;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
}

.login-steps {
  display: grid;
  gap: 1.5rem;
}

.login-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.api-key-example {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.api-key-example input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f5f5f5;
}

.demo-btn {
  background: #213d77;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.booking-process {
  display: grid;
  gap: 1.5rem;
}

.process-step {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  border-left: 4px solid #213d77;
}

.step-navigation {
  display: flex;
  justify-content: space-between;
  margin: 2rem 0;
}

.nav-btn {
  background: #213d77;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: #1a2f5f;
  transform: translateY(-2px);
}

.nav-btn.prev {
  background: #6c757d;
}

.setup-faq {
  margin: 3rem 0;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.setup-support {
  text-align: center;
  background: #f0f4f8;
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem 0;
}

.support-options {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.support-btn {
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.support-btn:not(.secondary) {
  background: #213d77;
  color: white;
}

.support-btn.secondary {
  background: transparent;
  color: #213d77;
  border: 2px solid #213d77;
}

.credit-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin: 20px 0;
}

.free-trial-option {
    background: linear-gradient(135deg, #ffffff, #d0fff0);
    color: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.free-trial-option h4 {
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.trial-btn {
  background: white;
  color: #10b981;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  display: inline-block;
  margin-top: 15px;
}

.paid-options {
  background: #f8fafc;
  padding: 25px;
  border-radius: 12px;
}

.social-login-demo {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
}

.demo-google-btn, .demo-facebook-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: default;
}

.demo-google-btn {
  background: #4285f4;
  color: white;
}

.demo-facebook-btn {
  background: #1877f2;
  color: white;
}

.success-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #ecfdf5;
  padding: 15px;
  border-radius: 8px;
  margin: 15px 0;
}

.checkmark {
  font-size: 1.2rem;
}

.automation-benefits {
  background: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.benefit {
  background: white;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .credit-options {
    grid-template-columns: 1fr;
  }
}

.important-notice {
  background: #fef3c7;
  border: 2px solid #f59e0b;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.important-notice h3 {
  color: #92400e;
  margin-bottom: 10px;
}

.non-refundable-list {
  background: #fef2f2;
  border-left: 4px solid #ef4444;
  padding: 20px;
  margin: 15px 0;
}

.compliance-info {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 6px;
  padding: 15px;
  margin: 15px 0;
}

.compliance-info p {
  margin: 5px 0;
  font-size: 0.9rem;
}

.policy-acknowledgment {
  background: #f3f4f6;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
  text-align: center;
}

.policy-acknowledgment em {
  font-weight: 600;
  color: #374151;
}


