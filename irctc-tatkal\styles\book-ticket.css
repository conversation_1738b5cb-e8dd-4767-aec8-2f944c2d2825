/* Base styles */
:root {
    --primary-color: #213d77;
    --primary-dark: #1a2f5f;
    --secondary-color: #34a853;
    --secondary-dark: #2e7d32;
    --danger-color: #ea4335;
    --warning-color: #fbbc05;
    --light-gray: #f5f5f5;
    --medium-gray: #e0e0e0;
    --dark-gray: #757575;
    --text-color: #333333;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-gray);
}

header h1 {
    font-size: 24px;
    color: var(--primary-color);
}

header h1 i {
    margin-right: 10px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info div {
    display: flex;
    flex-direction: column;
}

#user-name {
    font-weight: 600;
}

#credits-badge {
    font-size: 14px;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Main content layout */
main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

/* Form styles */
.booking-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.card-header {
    margin-bottom: 20px;
}

.card-header h2 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.booking-info {
    color: var(--dark-gray);
    font-size: 14px;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--medium-gray);
}

.form-section h3 {
    margin-bottom: 15px;
    color: var(--text-color);
    font-size: 18px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 14px;
}

.form-group small {
    font-size: 12px;
    color: var(--dark-gray);
    margin-top: 5px;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--dark-gray);
}

.input-with-icon input,
.input-with-icon select {
    padding-left: 35px;
}

input, select {
    padding: 10px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 14px;
    width: 100%;
}

input:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

/* Passenger card styles */
.passenger-card {
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
}

.passenger-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.passenger-header h4 {
    font-size: 16px;
    color: var(--text-color);
}

.btn-icon {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    font-size: 16px;
}

.btn-icon:disabled {
    color: var(--medium-gray);
    cursor: not-allowed;
}

/* Toggle switch styles */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--medium-gray);
    transition: .4s;
    border-radius: 34px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--secondary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* Payment method styles */
.payment-methods {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.payment-method {
    flex: 1;
    min-width: 120px;
}

.payment-method input[type="radio"] {
    display: none;
}

.payment-method label {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method label i {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--dark-gray);
}

.payment-method input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(66, 133, 244, 0.1);
}

.payment-method input[type="radio"]:checked + label i {
    color: var(--primary-color);
}

/* Button styles */
.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn-primary, .btn-secondary, .btn-outline {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: var(--medium-gray);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: rgba(66, 133, 244, 0.1);
}

/* Sidebar styles */
.info-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.info-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

.info-card h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--primary-color);
}

.info-card ul {
    list-style-type: none;
}

.info-card ul li {
    margin-bottom: 10px;
    font-size: 14px;
    position: relative;
    padding-left: 20px;
}

.info-card ul li:before {
    content: "•";
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.credits-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.credits-info p {
    font-size: 16px;
}

#sidebar-credits-count {
    font-weight: bold;
    color: var(--secondary-color);
}

.loading-text {
    color: var(--dark-gray);
    font-style: italic;
    font-size: 14px;
}

/* Station suggestions */
.station-suggestions {
    position: absolute;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid var(--medium-gray);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    z-index: 10;
    display: none;
}

.station-suggestion-item {
    padding: 10px;
    cursor: pointer;
}

.station-suggestion-item:hover {
    background-color: var(--light-gray);
}

/* Footer styles */
footer {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid var(--medium-gray);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.footer-links a:hover {
    text-decoration: underline;
}

.copyright {
    font-size: 12px;
    color: var(--dark-gray);
}

/* Responsive styles */
@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .form-actions {
        flex-direction: column;
        gap: 15px;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
    }

    .tab-navigation {
        flex-direction: column;
        gap: 10px;
    }

    .btn-prev, .btn-next {
        width: 100%;
        justify-content: center;
    }
}

/* Tab styles */
.form-tabs {
    display: flex;
    border-bottom: 1px solid var(--medium-gray);
    margin-top: 20px;
    overflow-x: auto;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: var(--dark-gray);
    white-space: nowrap;
}

.tab-btn.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
}

.tab-content {
    margin-top: 20px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.btn-prev, .btn-next {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    border: none;
}

.btn-prev {
    background-color: var(--light-gray);
    color: var(--text-color);
}

.btn-next {
    background-color: var(--primary-color);
    color: white;
}

.btn-prev:hover {
    background-color: var(--medium-gray);
}

.btn-next:hover {
    background-color: var(--primary-dark);
}

/* Additional styles for new elements */
.quota-timing-info {
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    padding: 15px;
}

.quota-timing {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.quota-timing:last-child {
    margin-bottom: 0;
}

.quota-name {
    font-weight: 500;
}

.quota-time {
    color: var(--primary-color);
    font-weight: 600;
}

.payment-detail-section {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
}

.payment-detail-section.hidden {
    display: none;
}

.payment-info {
    font-size: 14px;
    color: var(--dark-gray);
}

.no-infants-message {
    text-align: center;
    padding: 15px;
    color: var(--dark-gray);
    font-style: italic;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

/* Infant card styles */
.infant-card {
    background-color: var(--light-gray);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid var(--warning-color);
}

.infant-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.infant-header h4 {
    font-size: 16px;
    color: var(--text-color);
}

/* Tooltip for information */
.info-tooltip {
    position: relative;
    display: inline-block;
    margin-left: 5px;
    color: var(--primary-color);
    cursor: help;
}

.info-tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.info-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Passenger and Infant table styles */
.passenger-table-container, .infant-table-container {
    margin-bottom: 10px;
    overflow-x: auto;
}

/* Small button style */
.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    height: 24px;
}

.passenger-table, .infant-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.passenger-table th, .infant-table th {
    background-color: var(--light-gray);
    padding: 6px 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--medium-gray);
    font-size: 12px;
}

.passenger-table td, .infant-table td {
    padding: 6px 8px;
    border-bottom: 1px solid var(--medium-gray);
    vertical-align: middle;
}

.passenger-row, .infant-row {
    transition: background-color 0.2s;
}

.passenger-row:hover, .infant-row:hover {
    background-color: rgba(66, 133, 244, 0.05);
}

.passenger-table input, .passenger-table select,
.infant-table input, .infant-table select {
    padding: 4px 6px;
    height: 26px;
    font-size: 12px;
    border-radius: 3px;
    border: 1px solid var(--medium-gray);
}

.no-infants-message {
    text-align: center;
    padding: 10px;
    color: var(--dark-gray);
    font-style: italic;
    font-size: 12px;
}
