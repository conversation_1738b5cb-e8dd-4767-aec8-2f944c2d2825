.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.success-modal {
  background: white;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.success-header {
  text-align: center;
  padding: 32px 24px 24px 24px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.success-header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
}

.success-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.success-content {
  padding: 24px;
}

.purchase-summary {
  background: #f0f9ff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.purchase-summary h3 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.highlight {
  font-weight: 600;
  color: #059669;
  font-size: 16px;
}

.api-key-section {
  margin-bottom: 24px;
}

.api-key-section h3 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.api-key-section p {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
}

.api-key-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
}

.api-key {
  flex: 1;
  background: none;
  border: none;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #1f2937;
  word-break: break-all;
}

.copy-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  min-width: 40px;
}

.copy-btn:hover {
  background: #2563eb;
}

.copy-success {
  margin: 8px 0 0 0;
  color: #059669;
  font-size: 12px;
  font-weight: 500;
}

.next-steps {
  margin-bottom: 24px;
}

.next-steps h3 {
  margin: 0 0 20px 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.step-content p {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
}

.download-link {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.download-link:hover {
  background: #059669;
  text-decoration: none;
  color: white;
}

.email-notice {
  display: flex;
  gap: 12px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
}

.notice-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.notice-content h4 {
  margin: 0 0 4px 0;
  color: #92400e;
  font-size: 16px;
  font-weight: 600;
}

.notice-content p {
  margin: 0;
  color: #92400e;
  font-size: 14px;
  line-height: 1.4;
}

.success-actions {
  padding: 0 24px 24px 24px;
}

.close-btn {
  width: 100%;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 640px) {
  .success-modal-overlay {
    padding: 10px;
  }
  
  .success-modal {
    max-height: 95vh;
  }
  
  .success-header {
    padding: 24px 20px 20px 20px;
  }
  
  .success-content {
    padding: 20px;
  }
  
  .success-actions {
    padding: 0 20px 20px 20px;
  }
  
  .step {
    flex-direction: column;
    gap: 8px;
  }
  
  .step-number {
    align-self: flex-start;
  }
}
