.user-management {
  min-height: 100vh;
  background-color: #f8fafc;
}

.management-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 30px;
}

/* Users Section */
.users-section {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filters-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.search-box {
  flex: 1;
}

.search-box input {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #213d77;
  box-shadow: 0 0 0 3px rgba(33, 61, 119, 0.1);
}

.filters-section select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  gap: 16px;
}

.loading-section p {
  color: #64748b;
  font-size: 16px;
}

/* Users Table */
.users-table {
  overflow-x: auto;
  margin-bottom: 24px;
}

.users-table table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.users-table th {
  background-color: #f8fafc;
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.users-table td {
  color: #1f2937;
  font-size: 14px;
}

.users-table tr.selected {
  background-color: #eff6ff;
}

.users-table tr:hover {
  background-color: #f8fafc;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}

.status-select.active {
  background-color: #dcfce7;
  color: #16a34a;
  border-color: #bbf7d0;
}

.status-select.suspended {
  background-color: #fef3c7;
  color: #d97706;
  border-color: #fde68a;
}

.status-select.deleted {
  background-color: #fecaca;
  color: #dc2626;
  border-color: #fca5a5;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
}

.pagination button {
  padding: 8px 16px;
  background-color: #213d77;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination button:hover:not(:disabled) {
  background-color: #1a2f5f;
}

.pagination button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.pagination span {
  color: #6b7280;
  font-size: 14px;
}

/* User Details Section */
.user-details-section {
  position: sticky;
  top: 100px;
  height: fit-content;
}

.user-details-card {
  background: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.user-info h3 {
  margin: 0 0 4px 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.user-info p {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f3f4f6;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.stat-value {
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
  text-align: right;
  max-width: 200px;
  word-break: break-all;
}

.user-actions {
  display: flex;
  gap: 12px;
}

.add-credits-btn {
  flex: 1;
  padding: 10px 20px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-credits-btn:hover {
  background-color: #059669;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px 24px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 20px 24px;
}

.modal-body p {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
}

.modal-body input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.modal-body input:focus {
  outline: none;
  border-color: #213d77;
  box-shadow: 0 0 0 3px rgba(33, 61, 119, 0.1);
}

.modal-footer {
  padding: 0 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancel-btn {
  padding: 8px 16px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background-color: #e5e7eb;
}

.confirm-btn {
  padding: 8px 16px;
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn:hover {
  background-color: #059669;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .management-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .user-details-section {
    position: static;
  }
}

@media (max-width: 768px) {
  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .users-table th,
  .users-table td {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .user-cell {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .pagination {
    flex-direction: column;
    gap: 12px;
  }
  
  .user-header {
    flex-direction: column;
    text-align: center;
  }
  
  .user-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .users-section,
  .user-details-card {
    padding: 20px;
  }
  
  .modal {
    margin: 20px;
    width: calc(100% - 40px);
  }
}
