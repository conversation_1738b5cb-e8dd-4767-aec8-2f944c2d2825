import { useLocation, useNavigate } from 'react-router-dom';
import Layout from './Layout';
import '../styles/PurchaseSuccessPage.css';

const PurchaseSuccessPage = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const purchaseData = location.state;

  // Redirect to home if no purchase data
  if (!purchaseData) {
    navigate('/');
    return null;
  }



  return (
    <Layout>
      <div className="success-page">
        <div className="success-container">
          
          {/* Success Header */}
          <div className="success-header">
            <div className="success-icon">🎉</div>
            <h1>Payment Successful!</h1>
            <p>Your credits have been activated and are ready to use</p>
          </div>

          <div className="success-content">
            <div className="purchase-summary">
              <h2>Purchase Details</h2>
              <div className="summary-row">
                <span>Credits Purchased:</span>
                <span className="highlight">{purchaseData.credits}</span>
              </div>
              <div className="summary-row">
                <span>Email:</span>
                <span>{purchaseData.email}</span>
              </div>
            </div>

            <div className="email-section">
              <h2>✅ Credits Added Successfully</h2>
              <p>We've sent confirmation details to <strong>{purchaseData.email}</strong></p>
              <p>Credits purchased: <strong>{purchaseData.credits}</strong></p>
              <p>Your account now has <strong>{purchaseData.totalCredits || purchaseData.credits} total credits</strong> ready to use!</p>
            </div>

            <div className="next-steps">
              <h3>Next Steps</h3>
              <ol>
                <li>Download our Chrome extension</li>
                <li>Login with your email: <strong>{purchaseData.email}</strong></li>
                <li>Start booking tickets automatically</li>
              </ol>
            </div>

            {/* Support Section */}
            <div className="support-section">
              <h2>Need Help?</h2>
              <div className="support-options">
                <div className="support-option">
                  <div className="support-icon">💬</div>
                  <div className="support-text">
                    <h4>Live Chat Support</h4>
                    <p>Get instant help from our support team</p>
                  </div>
                </div>
                <div className="support-option">
                  <div className="support-icon">📖</div>
                  <div className="support-text">
                    <h4>Setup Guide</h4>
                    <p>Step-by-step instructions for getting started</p>
                  </div>
                </div>
                <div className="support-option">
                  <div className="support-icon">🎥</div>
                  <div className="support-text">
                    <h4>Video Tutorials</h4>
                    <p>Watch how to use the extension effectively</p>
                  </div>
                </div>
              </div>
            </div>

          </div>

          {/* Action Buttons */}
          <div className="action-buttons">
            <button 
              className="secondary-btn"
              onClick={() => navigate('/')}
            >
              Back to Home
            </button>
            <a 
              href="https://chrome.google.com/webstore/detail/irctc-tatkal-extension/YOUR_EXTENSION_ID" 
              target="_blank" 
              rel="noopener noreferrer"
              className="primary-btn"
            >
              Download Extension Now
            </a>
          </div>

        </div>
      </div>
    </Layout>
  );
};

export default PurchaseSuccessPage;




