.App {
  min-height: 100vh;
  background-color: #f8fafc;
}

/* Loading States */
.dashboard-loading,
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
}

.dashboard-loading p,
.admin-loading p {
  color: #64748b;
  font-size: 16px;
}

/* Common Header Styles */
.dashboard-header,
.admin-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info,
.admin-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info h2,
.admin-info h1 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.user-info p,
.admin-info p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.logout-btn,
.users-btn,
.dashboard-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover,
.users-btn:hover,
.dashboard-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.users-btn,
.dashboard-btn {
  background-color: #213d77;
  color: white;
  border-color: #213d77;
}

.users-btn:hover,
.dashboard-btn:hover {
  background-color: #1a2f5f;
  border-color: #1a2f5f;
}

/* Content Areas */
.dashboard-content,
.admin-content,
.management-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* Section Styles */
.section-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
}

.view-all-btn {
  padding: 8px 16px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background-color: #e5e7eb;
}

/* Card Styles */
.card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

/* No Data States */
.no-data,
.no-bookings {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-data p,
.no-bookings p {
  margin: 0;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .dashboard-content,
  .admin-content,
  .management-content {
    padding: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .user-info,
  .admin-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .logout-btn,
  .users-btn,
  .dashboard-btn {
    width: 100%;
    justify-content: center;
  }
}
