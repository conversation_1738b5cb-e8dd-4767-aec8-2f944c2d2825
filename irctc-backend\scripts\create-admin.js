require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Admin = require('../models/Admin');
const connectDB = require('../config/db');

// Connect to MongoDB
connectDB();

async function createAdmin() {
    try {
        // Check if admin already exists
        const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });
        
        if (existingAdmin) {
            console.log('Admin already exists with email: <EMAIL>');
            process.exit(0);
        }
        
        // Create a new admin
        const admin = new Admin({
            name: 'Admin User',
            email: '<EMAIL>',
            password: 'admin123' // This will be hashed by the pre-save hook
        });
        
        await admin.save();
        
        console.log('Admin created successfully:');
        console.log('Email: <EMAIL>');
        console.log('Password: admin123');
        
        process.exit(0);
    } catch (error) {
        console.error('Error creating admin:', error);
        process.exit(1);
    }
}

createAdmin();
