require('dotenv').config();
const mongoose = require('mongoose');
const Admin = require('../models/Admin');

const createAdmin = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        
        const adminData = {
            name: 'Admin User',
            email: '<EMAIL>',
            password: 'admin123' // This will be hashed automatically
        };

        const admin = new Admin(adminData);
        await admin.save();
        
        console.log('Admin user created successfully');
        process.exit(0);
    } catch (error) {
        console.error('Error creating admin:', error);
        process.exit(1);
    }
};

createAdmin();