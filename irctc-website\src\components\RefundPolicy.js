import React from 'react';
import { Link } from 'react-router-dom';
import Layout from './Layout';
import '../styles/StaticPages.css';

const RefundPolicy = () => {
  return (
    <Layout>
      <div className="static-page">
        <div className="static-container">
          <header className="static-header">
            <h1>Refund & Cancellation Policy</h1>
            <p className="subtitle">Understanding our no-refund policy and exceptional circumstances</p>
          </header>

          <div className="static-content">
            <section className="policy-section">
              <h2>No Refund Policy</h2>
              <p>
                <strong>IRCTC Auto operates on a strict no-refund policy.</strong> All credit purchases are final and non-refundable. 
                By purchasing credits, you acknowledge and agree to this policy.
              </p>
              <div className="important-notice">
                <h3>⚠️ Important Notice</h3>
                <p>Credits are consumed per booking attempt, regardless of booking success or failure. This is due to the service being provided and server resources being utilized for each attempt.</p>
              </div>
            </section>

            <section className="policy-section">
              <h2>Why No Refunds?</h2>
              <ul>
                <li><strong>Service Delivery:</strong> Our booking service is delivered immediately upon credit consumption</li>
                <li><strong>Resource Utilization:</strong> Server resources, API calls, and processes are used for each booking attempt</li>
                <li><strong>Digital Service Nature:</strong> Credits represent access to a digital service that cannot be "returned"</li>
                <li><strong>IRCTC Dependencies:</strong> Booking success depends on IRCTC availability, which is beyond our control</li>
              </ul>
            </section>

            <section className="policy-section">
              <h2>Credit Usage Terms</h2>
              <ul>
                <li>Credits are consumed when a booking attempt is initiated through our system</li>
                <li>Credits do not expire and remain in your account indefinitely</li>
                <li>Unused credits can be used for future bookings at any time</li>
                <li>Credits are non-transferable between accounts</li>
                <li>No refunds for unused credits under any circumstances</li>
              </ul>
            </section>

            <section className="policy-section">
              <h2>Exceptional Circumstances (Technical Issues Only)</h2>
              <p>Refunds may be considered <strong>only</strong> in the following rare technical circumstances:</p>
              <ul>
                <li><strong>Payment Processing Errors:</strong> If you are charged but credits are not added to your account due to payment gateway issues</li>
                <li><strong>Duplicate Charges:</strong> If you are accidentally charged multiple times for the same transaction</li>
                <li><strong>Unauthorized Transactions:</strong> If you can prove a transaction was made without your authorization (subject to investigation)</li>
                <li><strong>Service Unavailability:</strong> If our service is completely unavailable for more than 7 consecutive days</li>
              </ul>
              <p><strong>Note:</strong> IRCTC website issues, booking failures due to seat unavailability, or user errors do not qualify for refunds.</p>
            </section>

            <section className="policy-section">
              <h2>Dispute Resolution Process</h2>
              <div className="process-steps">
                <div className="step">
                  <h3>Step 1: Contact Support</h3>
                  <p>Email us at <a href="mailto:<EMAIL>"><EMAIL></a> within 48 hours of the issue</p>
                </div>
                <div className="step">
                  <h3>Step 2: Provide Evidence</h3>
                  <p>Include transaction ID, payment screenshot, and detailed description of the technical issue</p>
                </div>
                <div className="step">
                  <h3>Step 3: Investigation</h3>
                  <p>We will investigate technical issues within 3-5 business days</p>
                </div>
                <div className="step">
                  <h3>Step 4: Resolution</h3>
                  <p>If a technical error is confirmed, resolution will be provided within 7-10 business days</p>
                </div>
              </div>
            </section>

            <section className="policy-section">
              <h2>Required Information for Disputes</h2>
              <ul>
                <li>Razorpay transaction ID or payment reference number</li>
                <li>Email address associated with the purchase</li>
                <li>Screenshot of payment confirmation</li>
                <li>Detailed description of the technical issue</li>
                <li>Date and time of the incident</li>
              </ul>
            </section>

            <section className="policy-section">
              <h2>Refund Processing (If Approved)</h2>
              <p>In the rare event a refund is approved for technical issues:</p>
              <div className="timeline">
                <div className="timeline-item">
                  <strong>Credit Card:</strong> 5-7 business days
                </div>
                <div className="timeline-item">
                  <strong>Debit Card:</strong> 7-10 business days
                </div>
                <div className="timeline-item">
                  <strong>Net Banking:</strong> 7-10 business days
                </div>
                <div className="timeline-item">
                  <strong>UPI/Wallet:</strong> 3-5 business days
                </div>
              </div>
              <p><em>Refund timelines depend on your bank's processing time and are handled through Razorpay payment gateway.</em></p>
            </section>

            <section className="policy-section">
              <h2>Non-Refundable Scenarios</h2>
              <div className="non-refundable-list">
                <ul>
                  <li>Credits used for any booking attempts (successful or failed)</li>
                  <li>Change of mind after credit purchase</li>
                  <li>IRCTC website downtime or maintenance</li>
                  <li>Booking failures due to seat unavailability</li>
                  <li>User errors in booking details</li>
                  <li>Internet connectivity issues</li>
                  <li>Browser compatibility problems</li>
                  <li>Incorrect passenger information provided</li>
                  <li>Payment method failures during IRCTC checkout</li>
                </ul>
              </div>
            </section>

            <section className="policy-section">
              <h2>Free Trial Credits</h2>
              <p>
                Free trial credits (3 credits for new users) are provided as a courtesy to test our service. 
                These credits follow the same usage terms and are non-refundable.
              </p>
            </section>

            <section className="policy-section">
              <h2>Payment Gateway Compliance</h2>
              <p>
                This refund policy complies with Razorpay's merchant guidelines and Indian payment regulations. 
                All transactions are processed securely through Razorpay's certified payment gateway.
              </p>
              <div className="compliance-info">
                <p><strong>Payment Partner:</strong> Razorpay Software Private Limited</p>
                <p><strong>Merchant Category:</strong> Digital Services</p>
                <p><strong>Regulatory Compliance:</strong> RBI Guidelines for Digital Payments</p>
              </div>
            </section>

            <section className="policy-section">
              <h2>Contact Information</h2>
              <div className="contact-info">
                <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p><strong>Business Hours:</strong> Monday to Friday, 9:00 AM to 6:00 PM IST</p>
                <p><strong>Response Time:</strong> Within 24-48 hours for technical issues</p>
              </div>
            </section>

            <section className="policy-section">
              <h2>Policy Updates</h2>
              <p>
                This refund policy may be updated to comply with regulatory requirements or service changes. 
                Any updates will be posted on this page with an updated effective date. Continued use of our 
                service after policy changes constitutes acceptance of the new terms.
              </p>
              <p><strong>Last Updated:</strong> January 2025</p>
              <p><strong>Effective Date:</strong> January 1, 2025</p>
            </section>

            <div className="cta-section">
              <h2>Questions About This Policy?</h2>
              <p>If you have questions about our refund policy or need technical support, please contact us.</p>
              <div className="cta-buttons">
                <Link to="/support" className="btn btn-primary">Contact Support</Link>
                <a href="mailto:<EMAIL>" className="btn btn-secondary">Email Us</a>
              </div>
              <div className="policy-acknowledgment">
                <p><em>By purchasing credits, you acknowledge that you have read, understood, and agree to this no-refund policy.</em></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default RefundPolicy;
