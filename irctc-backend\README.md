# IRCTC Auto Booking Backend

## Environment URLs

### Development URLs
- **Home/Index**: http://localhost:3000/ (redirects to login or dashboard based on authentication)
- **Login Page**: http://localhost:3000/login
- **Admin Dashboard**: http://localhost:3000/dashboard
- **User Details**: http://localhost:3000/user-details?id=USER_ID (replace USER_ID with actual MongoDB ID)

### Production URLs (Render)
- **Home/Index**: https://your-render-app.onrender.com/
- **Login Page**: https://your-render-app.onrender.com/login
- **Admin Dashboard**: https://your-render-app.onrender.com/dashboard
- **API Base**: https://your-render-app.onrender.com/api

### Admin Credentials
- **Email**: <EMAIL>
- **Password**: Set via ADMIN_PASSWORD environment variable

## Features

### Dashboard
- View total users count
- View active users count
- View new users (last week) count
- List all users with pagination
- Filter users by status
- Update user status directly from the dashboard
- Dedicated column for available tickets
- Dedicated column with "View Details" button for each user

### User Details Page
- View comprehensive user information
- Add or remove tickets
- Update user status
- Navigate back to dashboard

## API Endpoints

### Authentication
- **POST /api/admin/login**: Admin login

### Admin Routes
- **GET /api/admin/stats**: Get dashboard statistics
- **GET /api/admin/users**: Get paginated list of users
- **GET /api/admin/users/:id**: Get details for a specific user
- **PATCH /api/admin/users/:id**: Update a user (tickets, status)
- **PATCH /api/admin/users/:id/status**: Update user status

## Development

### Running the Server
```
cd irctc-backend
node server.js
```

The server will start on port 3000 by default.
