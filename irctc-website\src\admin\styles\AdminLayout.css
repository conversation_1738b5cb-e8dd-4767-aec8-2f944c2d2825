.admin-layout {
  min-height: 100vh;
  background: #f8fafc;
}

/* Admin Header */
.admin-header {
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  padding: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.admin-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.admin-brand h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
}

.admin-subtitle {
  font-size: 12px;
  opacity: 0.8;
  display: block;
  margin-top: 2px;
}

.admin-user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.admin-name {
  font-size: 14px;
  font-weight: 600;
}

.admin-role {
  font-size: 12px;
  opacity: 0.8;
}

.admin-logout-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-logout-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Admin Main Layout */
.admin-main {
  display: flex;
  min-height: calc(100vh - 80px);
}

/* Admin Sidebar */
.admin-sidebar {
  width: 260px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.admin-nav {
  padding: 24px 0;
  flex: 1;
}

.admin-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 24px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.admin-nav-item:hover {
  background: #f3f4f6;
  color: #213d77;
}

.admin-nav-item.active {
  background: #f0f4ff;
  color: #213d77;
  border-right: 3px solid #213d77;
}

.nav-icon {
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.nav-label {
  flex: 1;
}

.admin-sidebar-footer {
  padding: 24px;
  border-top: 1px solid #e5e7eb;
}

.back-to-website-btn {
  width: 100%;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-to-website-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Admin Content */
.admin-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

/* Loading State */
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #213d77;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.error-message p {
  margin: 0;
  color: #dc2626;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .admin-sidebar {
    width: 220px;
  }
  
  .admin-content {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .admin-main {
    flex-direction: column;
  }
  
  .admin-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .admin-nav {
    padding: 16px 0;
    display: flex;
    overflow-x: auto;
  }
  
  .admin-nav-item {
    flex-shrink: 0;
    min-width: 120px;
    justify-content: center;
    padding: 12px 16px;
  }
  
  .admin-nav-item.active {
    border-right: none;
    border-bottom: 3px solid #213d77;
  }
  
  .admin-sidebar-footer {
    display: none;
  }
  
  .admin-content {
    padding: 20px;
  }
  
  .admin-header-content {
    padding: 12px 16px;
  }
  
  .admin-user-details {
    display: none;
  }
}
