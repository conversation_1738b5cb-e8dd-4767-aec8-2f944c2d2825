import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { adminAPI, handleApiError } from '../../services/api';
import AdminLayout from './AdminLayout';
import '../styles/AdminDashboard.css';

const AdminDashboard = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalCredits: 0,
    totalRevenue: 0,
    recentTransactions: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getStats();

      // Map backend response to frontend format
      const statsData = {
        totalUsers: response.data.totalUsers || 0,
        activeUsers: response.data.activeUsers || 0,
        totalCredits: response.data.totalCredits || 0,
        totalRevenue: response.data.totalRevenue || 0,
        recentTransactions: response.data.recentTransactions || []
      };

      setStats(statsData);
    } catch (error) {
      console.error('Error fetching stats:', error);
      setError(handleApiError(error));
      // Fallback to mock data if API fails
      const mockStats = {
        totalUsers: 0,
        activeUsers: 0,
        totalCredits: 0,
        totalRevenue: 0,
        recentTransactions: []
      };
      setStats(mockStats);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="admin-loading">
          <div className="loading-spinner"></div>
          <p>Loading dashboard...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="admin-dashboard">
        <div className="dashboard-header">
          <h1>Admin Dashboard</h1>
          <p>Overview of system statistics and recent activity</p>
        </div>

        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        {/* Stats Grid */}
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-icon">👥</div>
            <div className="stat-content">
              <h3>{stats.totalUsers}</h3>
              <p>Total Users</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">✅</div>
            <div className="stat-content">
              <h3>{stats.activeUsers}</h3>
              <p>Active Users</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">🎫</div>
            <div className="stat-content">
              <h3>{stats.totalCredits}</h3>
              <p>Total Credits</p>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <h3>₹{stats.totalRevenue}</h3>
              <p>Total Revenue</p>
            </div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div className="recent-section">
          <div className="section-header">
            <h2>Recent Transactions</h2>
            <button 
              className="view-all-btn"
              onClick={() => navigate('/admin/transactions')}
            >
              View All
            </button>
          </div>

          <div className="transactions-table">
            <table>
              <thead>
                <tr>
                  <th>User</th>
                  <th>Amount</th>
                  <th>Credits</th>
                  <th>Status</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                {stats.recentTransactions.length > 0 ? (
                  stats.recentTransactions.map((transaction) => (
                    <tr key={transaction._id}>
                      <td>
                        <div className="user-cell">
                          <img 
                            src={transaction.user?.picture || '/default-avatar.png'} 
                            alt="User" 
                            className="user-avatar-small"
                          />
                          <div>
                            <div className="user-name">{transaction.user?.name}</div>
                            <div className="user-email">{transaction.user?.email}</div>
                          </div>
                        </div>
                      </td>
                      <td>₹{transaction.amount}</td>
                      <td>{transaction.credits}</td>
                      <td>
                        <span className={`status-badge ${transaction.status}`}>
                          {transaction.status}
                        </span>
                      </td>
                      <td>{new Date(transaction.createdAt).toLocaleDateString()}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="5" className="no-data">
                      No recent transactions
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="quick-actions">
          <h2>Quick Actions</h2>
          <div className="actions-grid">
            <button 
              className="action-card"
              onClick={() => navigate('/admin/users')}
            >
              <div className="action-icon">👥</div>
              <div className="action-content">
                <h3>Manage Users</h3>
                <p>View and manage user accounts</p>
              </div>
            </button>

            <button 
              className="action-card"
              onClick={() => navigate('/admin/transactions')}
            >
              <div className="action-icon">💳</div>
              <div className="action-content">
                <h3>View Transactions</h3>
                <p>Monitor payment history</p>
              </div>
            </button>

            <button 
              className="action-card"
              onClick={() => navigate('/admin/settings')}
            >
              <div className="action-icon">⚙️</div>
              <div className="action-content">
                <h3>System Settings</h3>
                <p>Configure application settings</p>
              </div>
            </button>

            <button 
              className="action-card"
              onClick={() => navigate('/admin/reports')}
            >
              <div className="action-icon">📊</div>
              <div className="action-content">
                <h3>Reports</h3>
                <p>Generate system reports</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
