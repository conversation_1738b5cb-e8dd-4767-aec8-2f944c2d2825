import React, { useState } from 'react';
import '../styles/SuccessModal.css';

const SuccessModal = ({ isOpen, onClose, purchaseData }) => {
  const [copied, setCopied] = useState(false);

  const handleCopyApiKey = async () => {
    try {
      await navigator.clipboard.writeText(purchaseData?.apiKey);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy API key:', err);
    }
  };

  if (!isOpen || !purchaseData) return null;

  return (
    <div className="success-modal-overlay">
      <div className="success-modal">
        <div className="success-header">
          <div className="success-icon">🎉</div>
          <h2>Purchase Successful!</h2>
          <p>Your credits have been activated</p>
        </div>

        <div className="success-content">
          <div className="purchase-summary">
            <h3>Purchase Details</h3>
            <div className="summary-row">
              <span>Credits Purchased:</span>
              <span className="highlight">{purchaseData.credits}</span>
            </div>
            <div className="summary-row">
              <span>Email:</span>
              <span>{purchaseData.email}</span>
            </div>
          </div>

          <div className="api-key-section">
            <h3>Your API Key</h3>
            <p>Use this API key to login to the Chrome extension:</p>
            <div className="api-key-container">
              <code className="api-key">{purchaseData.apiKey}</code>
              <button 
                className="copy-btn"
                onClick={handleCopyApiKey}
                title="Copy API Key"
              >
                {copied ? '✓' : '📋'}
              </button>
            </div>
            {copied && <p className="copy-success">API key copied to clipboard!</p>}
          </div>

          <div className="next-steps">
            <h3>Next Steps</h3>
            <div className="steps">
              <div className="step">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h4>Download Extension</h4>
                  <p>Install our Chrome extension from the Chrome Web Store</p>
                  <a 
                    href="https://chrome.google.com/webstore/detail/irctc-tatkal-extension/YOUR_EXTENSION_ID" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="download-link"
                  >
                    Download Extension
                  </a>
                </div>
              </div>
              <div className="step">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h4>Login with API Key</h4>
                  <p>Use the API key above to login to the extension</p>
                </div>
              </div>
              <div className="step">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h4>Start Booking</h4>
                  <p>Fill the booking form and let the extension handle the rest</p>
                </div>
              </div>
            </div>
          </div>

          <div className="email-notice">
            <div className="notice-icon">📧</div>
            <div className="notice-content">
              <h4>Check Your Email</h4>
              <p>We've sent your API key and setup instructions to <strong>{purchaseData.email}</strong></p>
              <p>Your account now has <strong>{purchaseData.totalCredits || purchaseData.credits} total credits</strong></p>
            </div>
          </div>
        </div>

        <div className="success-actions">
          <button className="close-btn" onClick={onClose}>
            Got it, thanks!
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;

