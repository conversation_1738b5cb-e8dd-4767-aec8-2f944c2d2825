.success-page {
  min-height: calc(100vh - 140px);
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  padding: 40px 0;
}

.success-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Success Header */
.success-header {
  text-align: center;
  margin-bottom: 40px;
}

.success-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.success-header h1 {
  color: #065f46;
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.success-header p {
  color: #047857;
  font-size: 18px;
  margin: 0;
}

/* Success Content */
.success-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Purchase Summary */
.purchase-summary {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.purchase-summary h2 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.summary-grid {
  display: grid;
  gap: 12px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-item .label {
  color: #6b7280;
  font-size: 14px;
}

.summary-item .value {
  color: #1f2937;
  font-weight: 500;
  font-size: 14px;
}

.summary-item .value.highlight {
  color: #059669;
  font-weight: 600;
  font-size: 16px;
}

/* API Key Section */
.api-key-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.api-key-section h2 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.api-key-section p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 16px 0;
}

.api-key-container {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
}

.api-key {
  flex: 1;
  background: none;
  border: none;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #1f2937;
  word-break: break-all;
}

.copy-btn {
  background: #213d77;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.copy-btn:hover {
  background: #1a2f5f;
}

.copy-success {
  margin: 8px 0 0 0;
  color: #059669;
  font-size: 12px;
  font-weight: 500;
}

/* Next Steps */
.next-steps {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.next-steps h2 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.steps-grid {
  display: grid;
  gap: 20px;
}

.step {
  display: flex;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h3 {
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.step-content p {
  color: #6b7280;
  font-size: 14px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.download-link {
  display: inline-block;
  background: #10b981;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.download-link:hover {
  background: #059669;
  text-decoration: none;
  color: white;
}

/* Email Notice */
.email-notice {
  display: flex;
  gap: 16px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 12px;
  padding: 20px;
}

.notice-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.notice-content h3 {
  color: #92400e;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.notice-content p {
  color: #92400e;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}

/* Support Section */
.support-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.support-section h2 {
  color: #1f2937;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.support-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.support-option {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.support-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.support-text h4 {
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 2px 0;
}

.support-text p {
  color: #6b7280;
  font-size: 12px;
  margin: 0;
  line-height: 1.3;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

.secondary-btn {
  padding: 12px 24px;
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.secondary-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.primary-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #213d77 0%, #1a2f5f 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(33, 61, 119, 0.3);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 61, 119, 0.4);
  text-decoration: none;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .success-container {
    padding: 0 16px;
  }
  
  .success-header h1 {
    font-size: 28px;
  }
  
  .steps-grid {
    gap: 16px;
  }
  
  .step {
    flex-direction: column;
    gap: 8px;
  }
  
  .step-number {
    align-self: flex-start;
  }
  
  .support-options {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .success-page {
    padding: 20px 0;
  }
  
  .success-icon {
    font-size: 48px;
  }
  
  .success-header h1 {
    font-size: 24px;
  }
  
  .success-header p {
    font-size: 16px;
  }
  
  .purchase-summary,
  .api-key-section,
  .next-steps,
  .support-section {
    padding: 20px;
  }
}
