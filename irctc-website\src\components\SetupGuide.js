import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from './Layout';
import '../styles/StaticPages.css';

const SetupGuide = () => {
  const [activeStep, setActiveStep] = useState(1);

  const steps = [
    {
      id: 1,
      title: "Download & Install Extension",
      content: (
        <div className="step-content">
          <h3>Step 1: Install the Chrome Extension</h3>
          <div className="instruction-block">
            <p>First, you need to install our Chrome extension from the Chrome Web Store.</p>
            <div className="download-section">
              <a 
                href="https://chrome.google.com/webstore/detail/irctc-auto/YOUR_EXTENSION_ID" 
                target="_blank" 
                rel="noopener noreferrer"
                className="download-btn"
              >
                📥 Download Chrome Extension
              </a>
            </div>
            <div className="note">
              <strong>Note:</strong> Make sure you're using Google Chrome browser for the best experience.
            </div>
          </div>
          
          <div className="instruction-block">
            <h4>Installation Steps:</h4>
            <ol>
              <li>Click the download link above</li>
              <li>Click "Add to Chrome" on the Chrome Web Store page</li>
              <li>Confirm by clicking "Add extension" in the popup</li>
              <li>The extension icon will appear in your browser toolbar</li>
            </ol>
          </div>
        </div>
      )
    },
    {
      id: 2,
      title: "Get Credits",
      content: (
        <div className="step-content">
          <h3>Step 2: Get Credits to Start Booking</h3>
          <div className="instruction-block">
            <p>You need credits to use the automated booking service. Each booking attempt consumes 1 credit.</p>
            
            <div className="credit-options">
              <div className="free-trial-option">
                <h4>🎁 Free Trial Option</h4>
                <p>New users get <strong>3 free credits</strong> - no payment required!</p>
              </div>
              
              <div className="paid-options">
                <h4>💳 Purchase More Credits</h4>
                <div className="credit-packages">
                  <div className="package">
                    <h4>Starter Pack</h4>
                    <p>5 Credits - ₹99</p>
                  </div>
                  <div className="package popular">
                    <h4>Value Pack</h4>
                    <p>20 Credits - ₹299</p>
                    <span className="badge">Most Popular</span>
                  </div>
                  <div className="package">
                    <h4>Pro Pack</h4>
                    <p>40 Credits - ₹499</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="instruction-block">
            <h4>How to Get Credits:</h4>
            <ol>
              <li><strong>Free Trial:</strong> Sign up with Google/Facebook to get 3 free credits instantly</li>
              <li><strong>Purchase:</strong> Visit our homepage and select a credit package</li>
              <li>Enter your email and phone number</li>
              <li>Complete payment using Razorpay (UPI/Card/Net Banking)</li>
            </ol>
          </div>
        </div>
      )
    },
    {
      id: 3,
      title: "Login to Extension",
      content: (
        <div className="step-content">
          <h3>Step 3: Login to the Extension</h3>
          <div className="instruction-block">
            <p>Simply login with your Google or Facebook account</p>
            
            <div className="login-steps">
              <div className="login-step">
                <p>Click the IRCTC Auto extension icon in your browser toolbar</p>
              </div>
              <div className="login-step">
                <div className="social-login-demo">
                  <button className="demo-google-btn">🔵 Login with Google</button>
                  <button className="demo-facebook-btn">🔵 Login with Facebook</button>
                </div>
                <p>Choose your preferred social login method</p>
              </div>
              <div className="login-step">
                <div className="success-indicator">
                  <span className="checkmark">✅</span>
                  <span>Logged in successfully!</span>
                </div>
                <p>You'll be automatically authenticated and redirected to the dashboard</p>
              </div>
            </div>
          </div>
          
          <div className="note">
            <strong>Easy & Secure</strong>
          </div>
        </div>
      )
    },
    {
      id: 4,
      title: "Book Tickets",
      content: (
        <div className="step-content">
          <h3>Step 4: Automated Ticket Booking</h3>
          <div className="instruction-block">
            <p>Fill the extension form once, and it handles everything automatically!</p>
            
            <div className="booking-process">
              <div className="process-step">
                <h4>1. Open Extension Dashboard</h4>
                <p>Click the extension icon to open the booking dashboard</p>
              </div>
              
              <div className="process-step">
                <h4>2. Fill Booking Form (One Time)</h4>
                <p>Enter all your details in the extension form:</p>
                <ul>
                  <li>IRCTC login credentials (username & password)</li>
                  <li>Journey details (From, To, Date, Class)</li>
                  <li>Passenger information</li>
                  <li>Payment method preferences</li>
                </ul>
              </div>
              
              <div className="process-step">
                <h4>3. Start Automation</h4>
                <p>Click "Start Booking" and the extension will automatically:</p>
                <ul>
                  <li>Login to IRCTC with your credentials</li>
                  <li>Fill and submit all booking forms</li>
                  <li>Handle captcha and payment process</li>
                  <li>Complete the entire booking flow</li>
                  <li>Notify you of the booking status</li>
                </ul>
              </div>
              
              <div className="process-step">
                <h4>4. Sit Back & Relax</h4>
                <p>No manual intervention needed - the extension handles everything from login to payment page</p>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <Layout>
      <div className="static-page">
        <div className="static-container">
          <header className="static-header">
            <Link to="/" className="back-link">← Back to Home</Link>
            <h1>Setup Guide</h1>
            <p className="subtitle">Complete step-by-step guide to get started with IRCTC Auto</p>
          </header>

          <div className="setup-guide">
            {/* Progress Bar */}
            <div className="progress-bar">
              {steps.map((step) => (
                <div 
                  key={step.id}
                  className={`progress-step ${activeStep >= step.id ? 'active' : ''} ${activeStep === step.id ? 'current' : ''}`}
                  onClick={() => setActiveStep(step.id)}
                >
                  <div className="step-number">{step.id}</div>
                  <div className="step-title">{step.title}</div>
                </div>
              ))}
            </div>

            {/* Step Content */}
            <div className="step-container">
              {steps.find(step => step.id === activeStep)?.content}
            </div>

            {/* Navigation */}
            <div className="step-navigation">
              {activeStep > 1 && (
                <button 
                  className="nav-btn prev"
                  onClick={() => setActiveStep(activeStep - 1)}
                >
                  ← Previous
                </button>
              )}
              {activeStep < steps.length && (
                <button 
                  className="nav-btn next"
                  onClick={() => setActiveStep(activeStep + 1)}
                >
                  Next →
                </button>
              )}
            </div>
          </div>

          {/* Support Section */}
          <div className="setup-support">
            <h2>Need Help?</h2>
            <p>If you encounter any issues during setup, our support team is here to help.</p>
            <div className="support-options">
              <Link to="/support" className="support-btn">Contact Support</Link>
              <a href="mailto:<EMAIL>" className="support-btn secondary">Email Us</a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SetupGuide;
