import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { getCurrentAdmin, adminLogout } from '../../services/api';
import '../styles/AdminLayout.css';

const AdminLayout = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const admin = getCurrentAdmin();

  const handleLogout = () => {
    adminLogout();
  };

  const isActivePage = (path) => {
    return location.pathname === path;
  };

  const navigationItems = [
    { path: '/admin/dashboard', label: 'Dashboard', icon: '📊' },
    { path: '/admin/users', label: 'Users', icon: '👥' },
    { path: '/admin/transactions', label: 'Transactions', icon: '💳' },
    { path: '/admin/settings', label: 'Settings', icon: '⚙️' },
    { path: '/admin/reports', label: 'Reports', icon: '📈' }
  ];

  return (
    <div className="admin-layout">
      {/* Admin Header */}
      <header className="admin-header">
        <div className="admin-header-content">
          <div className="admin-brand">
            <h1>IRCTC Admin</h1>
            <span className="admin-subtitle">Management Dashboard</span>
          </div>

          <div className="admin-user-info">
            <div className="admin-user-details">
              <span className="admin-name">{admin?.name || 'Admin'}</span>
              <span className="admin-role">Administrator</span>
            </div>
            <button className="admin-logout-btn" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      <div className="admin-main">
        {/* Admin Sidebar */}
        <aside className="admin-sidebar">
          <nav className="admin-nav">
            {navigationItems.map((item) => (
              <button
                key={item.path}
                className={`admin-nav-item ${isActivePage(item.path) ? 'active' : ''}`}
                onClick={() => navigate(item.path)}
              >
                <span className="nav-icon">{item.icon}</span>
                <span className="nav-label">{item.label}</span>
              </button>
            ))}
          </nav>

          <div className="admin-sidebar-footer">
            <button 
              className="back-to-website-btn"
              onClick={() => navigate('/')}
            >
              ← Back to Website
            </button>
          </div>
        </aside>

        {/* Admin Content */}
        <main className="admin-content">
          {children}
        </main>
      </div>
    </div>
  );
};



export default AdminLayout;
