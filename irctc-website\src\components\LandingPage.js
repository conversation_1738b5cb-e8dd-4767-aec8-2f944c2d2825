import React from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from './Layout';
import '../styles/LandingPage.css';

const LandingPage = () => {
  const navigate = useNavigate();

  // Credit packages
  const creditPackages = [
    { id: 1, name: 'Starter Pack', credits: 5, price: 99 },
    { id: 2, name: 'Value Pack', credits: 20, price: 299, popular: true },
    { id: 3, name: 'Pro Pack', credits: 40, price: 499 }
  ];

  const handlePurchaseClick = (packageData) => {
    navigate(`/purchase/${packageData.id}`);
  };



  return (
    <Layout>
      <div className="landing-page">
        <div className="landing-container">

        {/* Hero Section */}
        <section className="hero-section">
          <div className="hero-content">
            <h2>Book IRCTC Tickets Easily</h2>
            <p>
              Get your IRCTC tickets booked automatically with our Chrome extension.
              Purchase credits and let our extension handle the booking process for you.
            </p>

            <div className="cta-buttons">
              <a
                href="https://chrome.google.com/webstore/detail/irctc-tatkal-extension/YOUR_EXTENSION_ID"
                target="_blank"
                rel="noopener noreferrer"
                className="download-btn primary"
              >
                📥 Download Chrome Extension
              </a>

              <p className="cta-subtitle">
                Download the extension first, then purchase credits below to start booking automatically!
              </p>
            </div>
          </div>

          <div className="hero-image">
            <img src="/hero-train.svg" alt="Train booking illustration" />
          </div>
        </section>


        {/* Free Trial Section */}
        <section className="free-trial-section">
          <div className="trial-container">
            <div className="trial-content">
              <h3>🎁 Start Your Free Trial</h3>
              <p>New to IRCTC Auto? Get <strong>3 free credits</strong> to use our service!</p>
              <div className="trial-features">
                <div className="trial-feature">✅ 3 free booking attempts</div>
                <div className="trial-feature">✅ No payment required</div>
                <div className="trial-feature">✅ Full extension access</div>
              </div>
              <button 
                className="trial-btn"
                onClick={() => navigate('/free-trial')}
              >
                Claim Free Trial
              </button>
            </div>
            <div className="trial-image">
              <div className="credit-badge">
                <span className="credit-number">3</span>
                <span className="credit-text">FREE<br/>CREDITS</span>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section className="pricing-section">
          <h3>Choose Your Credit Plan</h3>
          <p className="pricing-subtitle">Buy credits to use with the Chrome extension for automatic ticket booking</p>
          <div className="pricing-cards">
            {creditPackages.map((pkg) => (
              <div key={pkg.id} className={`pricing-card ${pkg.popular ? 'popular' : ''}`}>
                {pkg.popular && <div className="popular-badge">Most Popular</div>}
                <h4>{pkg.name}</h4>
                <div className="price">₹{pkg.price}</div>
                <div className="credits">{pkg.credits} Credits</div>
                <div className="price-per-credit">₹{(pkg.price / pkg.credits).toFixed(2)} per credit</div>
                <p>
                  {pkg.id === 1 && 'Perfect for occasional travelers'}
                  {pkg.id === 2 && 'Best value for regular travelers'}
                  {pkg.id === 3 && 'For frequent business travelers'}
                </p>
                <button
                  className="price-btn"
                  onClick={() => handlePurchaseClick(pkg)}
                >
                  Buy Now
                </button>
              </div>
            ))}
          </div>
        </section>

        {/* Features Section */}
        <section className="features-section">
          <h3>Why Choose Our Extension?</h3>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h4>Lightning Fast</h4>
              <p>Book tickets in seconds with automated form filling and submission</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🔒</div>
              <h4>Secure & Safe</h4>
              <p>Your data is encrypted and secure. We never store your payment details</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🎯</div>
              <h4>High Success Rate</h4>
              <p>Advanced algorithms ensure maximum booking success during Tatkal hours</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">💳</div>
              <h4>Credit System</h4>
              <p>Buy credits once and use them for multiple bookings. No recurring charges</p>
            </div>
          </div>
        </section>

        {/* How it Works Section */}
        <section className="how-it-works-section">
          <h3>How It Works</h3>
          <div className="steps-container">
            <div className="step">
              <div className="step-number">1</div>
              <h4>Download Extension</h4>
              <p>Install our Chrome extension from the Chrome Web Store</p>
            </div>
            <div className="step">
              <div className="step-number">2</div>
              <h4>Buy Credits</h4>
              <p>Purchase credits directly with your email - no account needed</p>
            </div>
            <div className="step">
              <div className="step-number">3</div>
              <h4>Book Automatically</h4>
              <p>Fill the form and let the extension book your Tatkal tickets</p>
            </div>
          </div>
        </section>

        </div>
      </div>
    </Layout>
  );
};

export default LandingPage;

