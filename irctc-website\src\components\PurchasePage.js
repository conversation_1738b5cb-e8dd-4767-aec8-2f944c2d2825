import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { paymentAPI, handleApiError } from '../services/api';
import Layout from './Layout';
import '../styles/PurchasePage.css';

const PurchasePage = () => {
  const { planId } = useParams();
  const navigate = useNavigate();
  
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    phone: ''
  });

  // Credit packages
  const creditPackages = [
    { id: 1, name: 'Starter Pack', credits: 5, price: 99 },
    { id: 2, name: 'Value Pack', credits: 20, price: 299, popular: true },
    { id: 3, name: 'Pro Pack', credits: 40, price: 499 }
  ];

  const selectedPackage = creditPackages.find(pkg => pkg.id === parseInt(planId)) || creditPackages[1];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.email || !formData.name) {
      setError('Please fill in all required fields');
      return false;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }
    
    return true;
  };

  const handlePayment = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError('');

      // Create order on backend with user details
      const orderResponse = await paymentAPI.createGuestOrder({
        amount: selectedPackage.price * 100, // Razorpay expects amount in paise
        currency: 'INR',
        credits: selectedPackage.credits,
        userDetails: formData
      });

      console.log('Order response:', orderResponse.data);

      if (!orderResponse.data.success) {
        throw new Error(orderResponse.data.message || 'Failed to create order');
      }

      const { orderId, amount, currency, key } = orderResponse.data;

      // Razorpay configuration
      const options = {
        key: key, // Use key from backend response
        amount: amount,
        currency: currency,
        name: 'IRCTC Auto',
        description: `${selectedPackage.credits} Credits Purchase`,
        order_id: orderId,
        handler: async function (response) {
          try {
            // Verify payment on backend
            const verifyResponse = await paymentAPI.verifyGuestPayment({
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              credits: selectedPackage.credits,
              amount: selectedPackage.price,
              userDetails: formData
            });

            if (verifyResponse.data.success) {
              // Navigate to success page with purchase data
              navigate('/purchase-success', {
                state: {
                  credits: selectedPackage.credits,
                  email: formData.email,
                  packageName: selectedPackage.name
                }
              });
            } else {
              setError('Payment verification failed. Please contact support.');
            }
          } catch (error) {
            console.error('Payment verification error:', error);
            setError('Payment verification failed. Please contact support.');
          }
        },
        prefill: {
          name: formData.name,
          email: formData.email,
          contact: formData.phone
        },
        notes: {
          credits: selectedPackage.credits,
          email: formData.email
        },
        theme: {
          color: '#213d77'
        },
        modal: {
          ondismiss: function() {
            setLoading(false);
          }
        }
      };

      // Load Razorpay script if not already loaded
      if (!window.Razorpay) {
        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => {
          const rzp = new window.Razorpay(options);
          rzp.open();
        };
        script.onerror = () => {
          setError('Failed to load payment gateway. Please try again.');
          setLoading(false);
        };
        document.body.appendChild(script);
      } else {
        const rzp = new window.Razorpay(options);
        rzp.open();
      }

    } catch (error) {
      console.error('Payment initiation error:', error);
      setError(handleApiError(error));
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="purchase-page">
        <div className="purchase-container">
          
          {/* Header */}
          <div className="purchase-header">
            <h1>Complete Your Purchase</h1>
            <p>You're just one step away from automated ticket booking!</p>
          </div>

          <div className="purchase-content">
            
            {/* Package Summary */}
            <div className="package-summary-card">
              <div className="package-header">
                <h2>{selectedPackage.name}</h2>
                {selectedPackage.popular && <span className="popular-badge">Most Popular</span>}
              </div>
              
              <div className="package-details">
                <div className="detail-item">
                  <span className="label">Credits:</span>
                  <span className="value">{selectedPackage.credits}</span>
                </div>
                <div className="detail-item">
                  <span className="label">Price per credit:</span>
                  <span className="value">₹{(selectedPackage.price / selectedPackage.credits).toFixed(2)}</span>
                </div>
                <div className="detail-item total">
                  <span className="label">Total Amount:</span>
                  <span className="value">₹{selectedPackage.price}</span>
                </div>
              </div>

              <div className="package-benefits">
                <h3>What You'll Get:</h3>
                <ul>
                  <li>✅ {selectedPackage.credits} credits for automatic ticket booking</li>
                  <li>✅ Instant activation after payment</li>
                  <li>✅ 24/7 customer supports</li>
                </ul>
              </div>
            </div>

            {/* User Form */}
            <div className="user-form-card">
              <h2>Your Details</h2>
              
              {error && (
                <div className="error-message">
                  <p>{error}</p>
                </div>
              )}

              <form onSubmit={(e) => e.preventDefault()}>
                <div className="form-group">
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email address"
                    required
                  />
                  <small>Use your IRCTC Auto extension email</small>
                </div>

                <div className="form-group">
                  <label htmlFor="name">Full Name *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phone">Phone Number (Optional)</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                  />
                </div>

                <div className="payment-info">
                  <div className="security-badges">
                    <div className="badge">🔒 SSL Secured</div>
                    <div className="badge">💳 All Cards Accepted</div>
                    <div className="badge">⚡ Instant Activation</div>
                  </div>
                </div>

                <button 
                  type="button"
                  className="pay-button"
                  onClick={handlePayment}
                  disabled={loading}
                >
                  {loading ? 'Processing...' : `Pay ₹${selectedPackage.price} Securely`}
                </button>

                <div className="terms-notice">
                  <p>
                    By proceeding, you agree to our{' '}
                    <a href="/terms" target="_blank" rel="noopener noreferrer">
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="/privacy" target="_blank" rel="noopener noreferrer">
                      Privacy Policy
                    </a>
                  </p>
                </div>
              </form>
            </div>

          </div>

          {/* Trust Indicators */}
          <div className="trust-section">
            <h3>Trusted by thousands of travelers</h3>
            <div className="trust-indicators">
              <div className="indicator">
                <div className="icon">🛡️</div>
                <div className="text">
                  <strong>Secure Payment</strong>
                  <p>Powered by Razorpay</p>
                </div>
              </div>
              <div className="indicator">
                <div className="icon">⚡</div>
                <div className="text">
                  <strong>Instant Setup</strong>
                  <p>Ready to use immediately</p>
                </div>
              </div>
              <div className="indicator">
                <div className="icon">🎯</div>
                <div className="text">
                  <strong>High Success Rate</strong>
                  <p>95%+ booking success</p>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </Layout>
  );
};

export default PurchasePage;





