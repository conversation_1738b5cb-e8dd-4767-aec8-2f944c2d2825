// Import all payment handlers
const paymentHandlers = {
    PAYTMUPIID: async () => {
        const { fillVPA } = await import('./PaytmVPA.js');
        return fillVPA();
    },
    PHONEPEUPIID: async () => {
        const { fillVPA } = await import('./PhonePeVPA.js');
        return fillVPA();
    },
    PHONEPEUPIQR: async () => {
        const { fillVPA } = await import('./PhonePeQR.js');
        return fillVPA();
    },
    IRCUPIQR: async () => {
        const { UpiVpaHandler } = await import('./irctc_upi.js');
        return UpiVpaHandler();
    },
    MOBUPIID: async () => {
        const { fillVPA } = await import('./UpiVPAPay.js');
        return fillVPA();
    }
};

// Listen for storage changes
chrome.storage.local.get(['other_preferences'], function(result) {
    const paymentMethod = result.other_preferences?.paymentmethod;
    if (paymentMethod && paymentHandlers[paymentMethod]) {
        paymentHandlers[paymentMethod]();
    }
});

// Export handlers for use in other scripts
export { paymentHandlers };
