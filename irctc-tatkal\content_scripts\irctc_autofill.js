let preferences = {};
let stations = {};
let vpa = {};
let other_preferences = {};

// Add retry mechanism for failed operations
async function retryOperation(operation, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await operation();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}

// Load preferences from storage
chrome.storage.local.get(['preferences', 'stations', 'vpa', 'other_preferences'], function(result) {
    if (chrome.runtime.lastError) {
        console.error('Storage error:', chrome.runtime.lastError);
        return;
    }
    preferences = result.preferences || {};
    stations = result.stations || {};
    vpa = result.vpa || {};
    other_preferences = result.other_preferences || {};
});

// Dynamically load payment handlers
async function loadPaymentHandlers() {
    const paymentHandlerScript = document.createElement('script');
    paymentHandlerScript.type = 'module';
    paymentHandlerScript.src = chrome.runtime.getURL('content_scripts/payment_handlers.js');
    document.head.appendChild(paymentHandlerScript);
}

// Listen for storage changes
chrome.storage.onChanged.addListener(function(changes, namespace) {
    for (let key in changes) {
        if (key === 'preferences') preferences = changes[key].newValue;
        if (key === 'stations') stations = changes[key].newValue;
        if (key === 'vpa') vpa = changes[key].newValue;
        if (key === 'other_preferences') other_preferences = changes[key].newValue;
    }
});

// Auto-fill station details
function fillStationDetails() {
    if (stations.source && stations.destination) {
        const sourceCode = stations.source.match(/\(([A-Z]+)\)$/)[1];
        const destCode = stations.destination.match(/\(([A-Z]+)\)$/)[1];

        // Fill source station
        const sourceInput = document.querySelector('#origin input');
        if (sourceInput) {
            sourceInput.value = sourceCode;
            sourceInput.dispatchEvent(new Event('input', { bubbles: true }));
            sourceInput.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Fill destination station
        const destInput = document.querySelector('#destination input');
        if (destInput) {
            destInput.value = destCode;
            destInput.dispatchEvent(new Event('input', { bubbles: true }));
            destInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }
}

// Handle Tatkal booking
function handleTatkalBooking() {
    if (preferences.autoBookTatkal) {
        // Select preferred class
        const classSelect = document.querySelector(`input[value="${preferences.preferredClass}"]`);
        if (classSelect) {
            classSelect.click();
        }

        // Set number of passengers
        const passengerCount = document.querySelector('#numberOfPassengers');
        if (passengerCount) {
            passengerCount.value = preferences.preferredPassengers;
            passengerCount.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }
}

// Handle payment method selection
function handlePayment() {
    const paymentMethod = preferences.paymentMethod;
    
    switch(paymentMethod) {
        case 'PAYTMUPIID':
            handlePaytmUPI();
            break;
        case 'PHONEPEUPIID':
            handlePhonePeUPI();
            break;
        case 'PHONEPEUPIQR':
            handlePhonePeQR();
            break;
        case 'IRCUPIQR':
            handleIRCTCQR();
            break;
        case 'MOBUPIID':
            handleGenericUPI();
            break;
    }
}

// Payment handler functions
function handlePaytmUPI() {
    // Implementation from paytm_upi.js
}

function handlePhonePeUPI() {
    // Implementation from phonepe_upi.js
}

function handlePhonePeQR() {
    // Implementation from PhonePeQR.js
}

function handleIRCTCQR() {
    // Implementation from irctc_upi.js
}

function handleGenericUPI() {
    // Implementation for generic UPI
}

// Initialize
(async function() {
    await loadPaymentHandlers();
    
    // Monitor for page changes
    const observer = new MutationObserver(function(mutations) {
        // Check for station inputs
        if (document.querySelector('#origin input') && document.querySelector('#destination input')) {
            fillStationDetails();
        }

        // Check for payment options
        if (document.querySelector('[data-test-id="payment-options"]')) {
            // Payment handling is now managed by the dynamically loaded payment_handlers.js
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
})();


