import React, { useState } from 'react';
import Layout from './Layout';
import '../styles/StaticPages.css';

const Support = () => {
  const [activeTab, setActiveTab] = useState('faq');
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);

  const handleInputChange = (e) => {
    setContactForm({
      ...contactForm,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    console.log('Support form submitted:', contactForm);
    setFormSubmitted(true);
    setContactForm({ name: '', email: '', subject: '', message: '' });
  };

  const faqData = [
    {
      question: "What are credits and how do they work?",
      answer: "Credits are the currency used in our IRCTC Auto system. Each credit allows you to make one automatic booking attempt on IRCTC. Credits are only consumed when a booking attempt is successfully made, regardless of whether the ticket booking succeeds or fails due to availability. You can purchase credits in packages or start with 3 free trial credits."
    },
    {
      question: "How does the IRCTC Auto work?",
      answer: "Our Chrome extension automates the ticket booking process on the IRCTC website. You fill out the booking details in our extension, and it automatically fills and submits the forms ."
    },
    {
      question: "Do I get free credits to try the service?",
      answer: "Yes! New users get 3 free trial credits to test our service. No payment required - just sign up with Google or Facebook and start booking immediately. After using your free credits, you can purchase more credit packages."
    },
    {
      question: "When are credits deducted from my account?",
      answer: "Credits are only deducted after a successful booking attempt is made on the IRCTC website. If our extension fails to initiate the booking process due to technical issues, your credits remain safe. However, if the booking attempt is made but fails due to IRCTC-related issues (like no seat availability), the credit is consumed."
    },
    {
      question: "Is my payment information secure?",
      answer: "Yes, absolutely. We use Razorpay, a trusted payment gateway, to process all payments. We never store your payment information on our servers. All transactions are encrypted and secure."
    },
    {
      question: "What happens if my booking fails?",
      answer: "If a booking fails due to technical issues on our end, we will refund the credit to your account. However, if the booking fails due to IRCTC-related issues (like no availability), the credit is consumed as the attempt was made."
    },
    {
      question: "Can I get a refund for unused credits?",
      answer: "Credits are generally non-refundable as mentioned in our Terms of Service. However, in exceptional circumstances, please contact our support team, and we'll review your case individually."
    },
    {
      question: "How do I install and set up the extension?",
      answer: "Download the extension from the Chrome Web Store, install it, and then sign-up with your social accounts like google or facebook. Follow the step-by-step guide in your user dashboard."
    },
    {
      question: "Why is my extension not working?",
      answer: "Common issues include: outdated extension version, browser compatibility issues, or IRCTC website changes. Try updating the extension and check for browser compatibility."
    },
    {
      question: "Can I use the extension on multiple devices?",
      answer: "Yes, you can use your account on multiple devices. However, simultaneous bookings from multiple devices may cause conflicts."
    }
  ];

  return (
    <Layout>
      <div className="static-page">
        <div className="static-container">
        <header className="static-header">
          {/* <Link to="/" className="back-link">← Back to Home</Link> */}
          <h1>Support Center</h1>
          <p className="subtitle">We're here to help you with any questions or issues</p>
        </header>

        <div className="support-tabs">
          <button
            className={`tab-btn ${activeTab === 'faq' ? 'active' : ''}`}
            onClick={() => setActiveTab('faq')}
          >
            FAQ
          </button>
          <button
            className={`tab-btn ${activeTab === 'contact' ? 'active' : ''}`}
            onClick={() => setActiveTab('contact')}
          >
            Contact Us
          </button>
        </div>

        <div className="static-content">
          {activeTab === 'faq' && (
            <section className="faq-section">
              <h2>Frequently Asked Questions</h2>
              <div className="faq-list">
                {faqData.map((faq, index) => (
                  <div key={index} className="faq-item">
                    <h3>{faq.question}</h3>
                    <p>{faq.answer}</p>
                  </div>
                ))}
              </div>
            </section>
          )}

          {activeTab === 'contact' && (
            <section className="contact-section">
              <h2>Contact Our Support Team</h2>

              <div className="contact-methods">
                <div className="contact-method">
                  <h3>📧 Email Support</h3>
                  <p>Get help via email within 24 hours</p>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>


              </div>

              <div className="contact-form-container">
                <h3>Send us a Message</h3>
                {formSubmitted ? (
                  <div className="form-success">
                    <h4>✅ Message Sent Successfully!</h4>
                    <p>Thank you for contacting us. We'll get back to you within 24 hours.</p>
                  </div>
                ) : (
                  <form className="contact-form" onSubmit={handleSubmit}>
                    <div className="form-group">
                      <label htmlFor="name">Name *</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={contactForm.name}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="email">Email *</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={contactForm.email}
                        onChange={handleInputChange}
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="subject">Subject *</label>
                      <select
                        id="subject"
                        name="subject"
                        value={contactForm.subject}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">Select a subject</option>
                        <option value="technical">Technical Issue</option>
                        <option value="billing">Billing & Payments</option>
                        <option value="refund">Refund Request</option>
                        <option value="feature">Feature Request</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div className="form-group">
                      <label htmlFor="message">Message *</label>
                      <textarea
                        id="message"
                        name="message"
                        rows="5"
                        value={contactForm.message}
                        onChange={handleInputChange}
                        placeholder="Please describe your issue or question in detail..."
                        required
                      ></textarea>
                    </div>

                    <button type="submit" className="submit-btn">
                      Send Message
                    </button>
                  </form>
                )}
              </div>
            </section>
          )}

        </div>
      </div>
    </div>
    </Layout>
  );
};

export default Support;


