const express = require('express');
const router = express.Router();
const User = require('../models/User'); // Fix the case sensitivity
const auth = require('../middleware/auth');

// Get available tickets count
router.get('/count', auth, async (req, res) => {
    try {
        console.log('Received request for tickets count');
        console.log('User ID:', req.user.id);

        const user = await User.findById(req.user.id);
        if (!user) {
            console.log('User not found in database');
            return res.status(404).json({
                message: 'User not found',
                success: false
            });
        }

        console.log('User found:', {
            email: user.email,
            credits: user.credits
        });

        res.json({
            success: true,
            count: user.credits || 0,
            message: 'Tickets count retrieved successfully'
        });
    } catch (error) {
        console.error('Error in /count endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
});

// Purchase tickets
router.post('/purchase', auth, async (req, res) => {
    try {
        const { quantity } = req.body;
        const user = await User.findById(req.user.id);
        user.credits += quantity;
        await user.save();
        res.json({
            success: true,
            message: 'Tickets purchased successfully',
            newCount: user.credits
        });
    } catch (error) {
        console.error('Purchase error:', error);
        res.status(500).json({
            success: false,
            message: 'Purchase failed',
            error: error.message
        });
    }
});

// Get booked tickets
router.get('/booked', auth, async (req, res) => {
    try {
        console.log('Received request for booked tickets');
        console.log('User ID:', req.user.id);

        const user = await User.findById(req.user.id);
        if (!user) {
            console.log('User not found in database');
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log('User found, booked tickets:', user.bookedTickets?.length || 0);

        res.json({
            success: true,
            tickets: user.bookedTickets || [],
            message: 'Booked tickets retrieved successfully'
        });
    } catch (error) {
        console.error('Error in /booked endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
});

// Get a single ticket by ID
router.get('/:id', auth, async (req, res) => {
    try {
        console.log('Received request for ticket details');
        console.log('User ID:', req.user.id);
        console.log('Ticket ID:', req.params.id);

        const user = await User.findById(req.user.id);
        if (!user) {
            console.log('User not found in database');
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Find the ticket in the user's booked tickets
        const ticket = user.bookedTickets.id(req.params.id);
        if (!ticket) {
            console.log('Ticket not found');
            return res.status(404).json({
                success: false,
                message: 'Ticket not found'
            });
        }

        console.log('Ticket found:', ticket);

        // Enhance ticket with additional details for the frontend
        const enhancedTicket = {
            ...ticket.toObject(),
            fromStation: {
                name: ticket.fromStation.split(' (')[0],
                code: ticket.fromStation.split(' (')[1]?.replace(')', '') || ''
            },
            toStation: {
                name: ticket.toStation.split(' (')[0],
                code: ticket.toStation.split(' (')[1]?.replace(')', '') || ''
            },
            trainNumber: ticket.trainNumber || '00000',
            trainName: ticket.trainName || 'Unknown',
            boardingStation: ticket.boardingStation ? {
                name: ticket.boardingStation.split(' (')[0],
                code: ticket.boardingStation.split(' (')[1]?.replace(')', '') || ''
            } : null,
            passengers: ticket.passengers || [],
            infants: ticket.infants || [],
            mobileNumber: ticket.mobileNumber || '',
            paymentMethod: ticket.paymentMethod || '',
            transactionId: ticket.transactionId || ''
        };

        res.json({
            success: true,
            ticket: enhancedTicket,
            message: 'Ticket details retrieved successfully'
        });
    } catch (error) {
        console.error('Error in /:id endpoint:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
});

// Book a new ticket
router.post('/book', auth, async (req, res) => {
    try {
        const { 
            fromStation, 
            toStation, 
            journeyDate, 
            class: travelClass,
            bookingStatus,
            pnr 
        } = req.body;

        // Validate input
        if (!fromStation || !toStation || !journeyDate || !travelClass) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields'
            });
        }

        const user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Only decrement credits if booking was successful
        if (bookingStatus === 'success') {
            // Check if user has available tickets
            if (user.credits <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'No available tickets'
                });
            }

            // Create a new ticket record
            const newTicket = {
                fromStation,
                toStation,
                journeyDate,
                class: travelClass,
                pnr: pnr || 'PNR' + Math.floor(Math.random() * 10000000),
                bookingDate: new Date(),
                status: 'confirmed'
            };

            // Add to user's booked tickets
            user.bookedTickets.push(newTicket);

            // Deduct a credit only after successful booking
            user.credits -= 1;

            await user.save();

            res.json({
                success: true,
                message: 'Ticket booked successfully and credit deducted',
                ticket: newTicket,
                credits: user.credits
            });
        } else {
            // Just log the attempt without deducting credits
            res.json({
                success: true,
                message: 'Booking attempt logged, no credits deducted',
                credits: user.credits
            });
        }
    } catch (error) {
        console.error('Error booking ticket:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to book ticket',
            error: error.message
        });
    }
});

// Cancel a booked ticket
router.post('/cancel/:ticketId', auth, async (req, res) => {
    try {
        const { ticketId } = req.params;

        const user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Find the ticket
        const ticketIndex = user.bookedTickets.findIndex(ticket => ticket._id.toString() === ticketId);

        if (ticketIndex === -1) {
            return res.status(404).json({
                success: false,
                message: 'Ticket not found'
            });
        }

        // Update ticket status to cancelled
        user.bookedTickets[ticketIndex].status = 'cancelled';

        // Refund an available ticket
        user.credits += 1;

        await user.save();

        res.json({
            success: true,
            message: 'Ticket cancelled successfully',
            credits: user.credits
        });
    } catch (error) {
        console.error('Error cancelling ticket:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to cancel ticket',
            error: error.message
        });
    }
});

module.exports = router;

